# باقي المكونات - خطة التطوير

## اليوم 11-12: قسم "من نحن" التفاعلي

### 1. قسم من نحن مع Timeline
```html
<!-- About Section -->
<section class="about-section">
    <div class="container">
        <div class="about-content">
            <div class="about-text">
                <h2 class="section-title">من نحن</h2>
                <p class="about-description">
                    نحن شركة رائدة في صناعة الورق مع خبرة تزيد عن 15 عاماً
                </p>
                <div class="about-stats">
                    <div class="stat-item">
                        <span class="stat-number" data-count="15">0</span>
                        <span class="stat-label">سنة خبرة</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" data-count="500">0</span>
                        <span class="stat-label">عميل راضي</span>
                    </div>
                </div>
            </div>
            <div class="about-image">
                <img src="about-us.jpg" alt="من نحن">
            </div>
        </div>
        
        <!-- Timeline -->
        <div class="timeline-section">
            <h3 class="timeline-title">رحلتنا عبر السنين</h3>
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-year">2008</div>
                    <div class="timeline-content">
                        <h4>تأسيس الشركة</h4>
                        <p>بداية رحلتنا في صناعة الورق</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-year">2012</div>
                    <div class="timeline-content">
                        <h4>التوسع الأول</h4>
                        <p>افتتاح المصنع الثاني</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-year">2018</div>
                    <div class="timeline-content">
                        <h4>شهادات الجودة</h4>
                        <p>حصولنا على شهادات ISO</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-year">2024</div>
                    <div class="timeline-content">
                        <h4>التقنيات الحديثة</h4>
                        <p>استخدام أحدث التقنيات</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
```

### 2. CSS للـ Timeline:
```css
.timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 0;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #007bff;
    transform: translateX(-50%);
}

.timeline-item {
    position: relative;
    margin: 2rem 0;
    display: flex;
    align-items: center;
}

.timeline-item:nth-child(odd) {
    flex-direction: row;
}

.timeline-item:nth-child(even) {
    flex-direction: row-reverse;
}

.timeline-year {
    background: #007bff;
    color: white;
    padding: 1rem;
    border-radius: 50%;
    font-weight: bold;
    min-width: 80px;
    text-align: center;
    position: relative;
    z-index: 2;
}

.timeline-content {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin: 0 2rem;
    flex: 1;
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s ease;
}

.timeline-content.animate {
    opacity: 1;
    transform: translateY(0);
}
```

## اليوم 13: قسم الخدمات

### 1. قسم الخدمات التفاعلي
```html
<!-- Services Section -->
<section class="services-section">
    <div class="container">
        <h2 class="section-title">خدماتنا</h2>
        <div class="services-grid">
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-industry"></i>
                </div>
                <h3 class="service-title">التصنيع</h3>
                <p class="service-description">
                    تصنيع جميع أنواع الورق بأحدث التقنيات
                </p>
                <ul class="service-features">
                    <li>جودة عالية</li>
                    <li>أسعار تنافسية</li>
                    <li>تسليم سريع</li>
                </ul>
                <button class="service-btn">اعرف المزيد</button>
            </div>
            
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-shipping-fast"></i>
                </div>
                <h3 class="service-title">التوصيل</h3>
                <p class="service-description">
                    خدمة توصيل سريعة وآمنة
                </p>
                <ul class="service-features">
                    <li>توصيل مجاني</li>
                    <li>تتبع الشحنة</li>
                    <li>ضمان الوصول</li>
                </ul>
                <button class="service-btn">اعرف المزيد</button>
            </div>
            
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-headset"></i>
                </div>
                <h3 class="service-title">الدعم الفني</h3>
                <p class="service-description">
                    دعم فني متخصص على مدار الساعة
                </p>
                <ul class="service-features">
                    <li>دعم 24/7</li>
                    <li>استشارات مجانية</li>
                    <li>حلول مخصصة</li>
                </ul>
                <button class="service-btn">اعرف المزيد</button>
            </div>
        </div>
    </div>
</section>
```

## اليوم 14: قسم التواصل المتقدم

### 1. نموذج التواصل التفاعلي
```html
<!-- Contact Section -->
<section class="contact-section">
    <div class="container">
        <div class="contact-content">
            <div class="contact-info">
                <h2>تواصل معنا</h2>
                <p>نحن هنا لخدمتك في أي وقت</p>
                
                <div class="contact-methods">
                    <div class="contact-method">
                        <div class="method-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="method-info">
                            <h4>الهاتف</h4>
                            <p>+20 123 456 789</p>
                        </div>
                    </div>
                    
                    <div class="contact-method">
                        <div class="method-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="method-info">
                            <h4>البريد الإلكتروني</h4>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    
                    <div class="contact-method">
                        <div class="method-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="method-info">
                            <h4>العنوان</h4>
                            <p>القاهرة، مصر</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="contact-form">
                <form id="contact-form">
                    <div class="form-group">
                        <input type="text" id="name" required>
                        <label for="name">الاسم</label>
                    </div>
                    
                    <div class="form-group">
                        <input type="email" id="email" required>
                        <label for="email">البريد الإلكتروني</label>
                    </div>
                    
                    <div class="form-group">
                        <input type="tel" id="phone" required>
                        <label for="phone">رقم الهاتف</label>
                    </div>
                    
                    <div class="form-group">
                        <select id="subject" required>
                            <option value="">اختر الموضوع</option>
                            <option value="quote">طلب عرض سعر</option>
                            <option value="sample">طلب عينة</option>
                            <option value="support">دعم فني</option>
                            <option value="other">أخرى</option>
                        </select>
                        <label for="subject">الموضوع</label>
                    </div>
                    
                    <div class="form-group">
                        <textarea id="message" required></textarea>
                        <label for="message">الرسالة</label>
                    </div>
                    
                    <button type="submit" class="submit-btn">
                        <span class="btn-text">إرسال الرسالة</span>
                        <span class="btn-loading">جاري الإرسال...</span>
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Interactive Map -->
        <div class="map-section">
            <div id="company-map"></div>
        </div>
    </div>
</section>
```

### 2. JavaScript للنموذج:
```javascript
// Contact Form Handling
document.getElementById('contact-form').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const submitBtn = document.querySelector('.submit-btn');
    const btnText = document.querySelector('.btn-text');
    const btnLoading = document.querySelector('.btn-loading');
    
    // Show loading state
    submitBtn.classList.add('loading');
    btnText.style.display = 'none';
    btnLoading.style.display = 'inline';
    
    // Collect form data
    const formData = new FormData(e.target);
    
    try {
        const response = await fetch('/contact', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });
        
        if (response.ok) {
            // Success
            showNotification('تم إرسال رسالتك بنجاح!', 'success');
            e.target.reset();
        } else {
            throw new Error('فشل في الإرسال');
        }
    } catch (error) {
        showNotification('حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
    } finally {
        // Reset button state
        submitBtn.classList.remove('loading');
        btnText.style.display = 'inline';
        btnLoading.style.display = 'none';
    }
});

// Notification System
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}
```

## الملفات المطلوبة للأسبوع الثاني:

```
resources/views/sections/
├── about-advanced.blade.php
├── services-interactive.blade.php
├── contact-modern.blade.php
└── timeline.blade.php

public/assets/css/
├── about-section.css
├── services-section.css
├── contact-section.css
└── timeline.css

public/assets/js/
├── about-animations.js
├── services-interactions.js
├── contact-form.js
└── timeline-scroll.js
```
