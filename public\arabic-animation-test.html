<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arabic Text Animation Test</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/vendor/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/arabic-text-animation.css">
    
    <!-- Google Fonts for Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Noto+Sans+Arabic:wght@100..900&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', 'Noto <PERSON>s Arabic', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 50px 0;
        }
        
        .test-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin: 20px auto;
            max-width: 1200px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin: 60px 0;
            padding: 30px;
            border: 2px solid #f0f0f0;
            border-radius: 15px;
            background: #fafafa;
        }
        
        .test-title {
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: 600;
        }
        
        .animated-text {
            font-size: 36px;
            font-weight: 700;
            line-height: 1.4;
            margin: 30px 0;
            color: #2c3e50;
        }
        
        .arabic-text {
            font-family: 'Cairo', 'Noto Sans Arabic', Arial, sans-serif;
            direction: rtl;
            text-align: right;
        }
        
        .english-text {
            font-family: 'Red Hat Display', Arial, sans-serif;
            direction: ltr;
            text-align: left;
        }
        
        .demo-info {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #2196f3;
        }
        
        .demo-info h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        
        .demo-info p {
            color: #424242;
            margin: 5px 0;
        }
        
        .scroll-trigger {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-5">Arabic Text Animation Test</h1>
        
        <!-- English Text Animation Test -->
        <div class="test-section">
            <h3 class="test-title">English Text Animation (Character by Character)</h3>
            <div class="demo-info">
                <h4>Animation Details:</h4>
                <p>• Animates character by character</p>
                <p>• Direction: Left to Right (x: 50 → 0)</p>
                <p>• Stagger: 0.02s between characters</p>
                <p>• Uses rotateX transformation</p>
            </div>
            <div class="scroll-trigger">
                <h2 class="animated-text english-text rts-text-anime-style-1">
                    Discover Paper Recycling Solutions
                </h2>
            </div>
        </div>
        
        <!-- Arabic Text Animation Test -->
        <div class="test-section">
            <h3 class="test-title">Arabic Text Animation (Word by Word)</h3>
            <div class="demo-info">
                <h4>Animation Details:</h4>
                <p>• Animates word by word to preserve Arabic character connections</p>
                <p>• Direction: Right to Left (x: -50 → 0)</p>
                <p>• Stagger: 0.08s between words</p>
                <p>• Uses rotateY transformation</p>
                <p>• Maintains proper Arabic text shaping</p>
            </div>
            <div class="scroll-trigger">
                <h2 class="animated-text arabic-text rts-text-anime-style-1" dir="rtl" lang="ar">
                    أكتشف حلول إعادة تدوير الورق المستدامة
                </h2>
            </div>
        </div>
        
        <!-- Mixed Content Test -->
        <div class="test-section">
            <h3 class="test-title">Mixed Content Test</h3>
            <div class="demo-info">
                <h4>Animation Details:</h4>
                <p>• Automatically detects Arabic characters</p>
                <p>• Applies appropriate animation based on content</p>
                <p>• Handles mixed language content gracefully</p>
            </div>
            <div class="scroll-trigger">
                <h2 class="animated-text rts-text-anime-style-1">
                    WARAQ CO - شركة ورق للتدوير
                </h2>
            </div>
        </div>
        
        <!-- Long Arabic Text Test -->
        <div class="test-section">
            <h3 class="test-title">Long Arabic Text Test</h3>
            <div class="demo-info">
                <h4>Animation Details:</h4>
                <p>• Tests performance with longer Arabic text</p>
                <p>• Maintains readability during animation</p>
                <p>• Proper line breaks and text flow</p>
            </div>
            <div class="scroll-trigger">
                <h2 class="animated-text arabic-text rts-text-anime-style-1" dir="rtl" lang="ar">
                    في شركة ورق، نمنح الأوراق المستعملة فرصة جديدة لتُصبح جزءًا من حل بيئي مستدام
                </h2>
            </div>
        </div>
        
        <!-- Performance Test -->
        <div class="test-section">
            <h3 class="test-title">Performance & Accessibility</h3>
            <div class="demo-info">
                <h4>Features:</h4>
                <p>• GPU acceleration for smooth animations</p>
                <p>• Respects prefers-reduced-motion settings</p>
                <p>• Optimized for mobile devices</p>
                <p>• Cross-browser compatibility</p>
                <p>• Proper Arabic font rendering</p>
            </div>
            <div class="scroll-trigger">
                <h2 class="animated-text arabic-text rts-text-anime-style-1" dir="rtl" lang="ar">
                    تقنية متطورة وصديقة للبيئة
                </h2>
            </div>
        </div>
    </div>
    
    <!-- JavaScript Files -->
    <script src="assets/js/plugins/jquery.js"></script>
    <script src="assets/js/plugins/gsap.js"></script>
    <script src="assets/js/plugins/split-text.js"></script>
    <script src="assets/js/plugins/scroll-trigger.js"></script>
    <script src="assets/js/main.js"></script>
    
    <script>
        // Additional test script to demonstrate functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Arabic Text Animation Test Page Loaded');
            
            // Log when animations trigger
            const animatedElements = document.querySelectorAll('.rts-text-anime-style-1');
            animatedElements.forEach((element, index) => {
                const isArabic = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/.test(element.textContent);
                console.log(`Element ${index + 1}: ${isArabic ? 'Arabic' : 'English'} - "${element.textContent.trim()}"`);
            });
        });
    </script>
</body>
</html>
