# صفحة المنتجات التفاعلية - خطة التطوير

## المهام المطلوبة:

### 1. معرض المنتجات المتقدم
```html
<!-- Products Gallery -->
<section class="products-gallery">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">منتجاتنا المميزة</h2>
            <p class="section-subtitle">أحدث تقنيات صناعة الورق</p>
        </div>
        
        <div class="products-filter">
            <button class="filter-btn active" data-filter="all">الكل</button>
            <button class="filter-btn" data-filter="packaging">ورق التغليف</button>
            <button class="filter-btn" data-filter="printing">ورق الطباعة</button>
            <button class="filter-btn" data-filter="cardboard">الكرتون</button>
        </div>
        
        <div class="products-grid">
            <div class="product-card" data-category="packaging">
                <div class="product-image">
                    <img src="product1.jpg" alt="منتج 1">
                    <div class="product-overlay">
                        <div class="product-actions">
                            <button class="btn-view" data-product="1">عرض سريع</button>
                            <button class="btn-360" data-product="1">عرض 360°</button>
                            <button class="btn-download">تحميل الكتالوج</button>
                        </div>
                    </div>
                </div>
                <div class="product-info">
                    <h3 class="product-title">ورق تغليف عالي الجودة</h3>
                    <p class="product-description">مناسب لجميع أنواع التغليف الصناعي</p>
                    <div class="product-specs">
                        <span class="spec">الوزن: 120-250 جم/م²</span>
                        <span class="spec">المقاس: حسب الطلب</span>
                    </div>
                    <div class="product-footer">
                        <button class="btn-quote">طلب عرض سعر</button>
                        <button class="btn-sample">طلب عينة</button>
                    </div>
                </div>
            </div>
            
            <!-- المزيد من المنتجات... -->
        </div>
    </div>
</section>
```

### 2. نافذة العرض السريع
```html
<!-- Quick View Modal -->
<div class="quick-view-modal">
    <div class="modal-content">
        <button class="modal-close">×</button>
        <div class="modal-body">
            <div class="product-images">
                <div class="main-image">
                    <img id="main-product-image" src="" alt="">
                </div>
                <div class="thumbnail-images">
                    <!-- صور مصغرة -->
                </div>
            </div>
            <div class="product-details">
                <h2 id="modal-product-title"></h2>
                <p id="modal-product-description"></p>
                
                <div class="product-specifications">
                    <h4>المواصفات التقنية:</h4>
                    <table class="specs-table">
                        <tr>
                            <td>الوزن</td>
                            <td id="spec-weight"></td>
                        </tr>
                        <tr>
                            <td>المقاس</td>
                            <td id="spec-size"></td>
                        </tr>
                        <tr>
                            <td>اللون</td>
                            <td id="spec-color"></td>
                        </tr>
                    </table>
                </div>
                
                <div class="product-actions-modal">
                    <button class="btn-primary">طلب عرض سعر</button>
                    <button class="btn-secondary">تحميل الكتالوج</button>
                    <button class="btn-outline">طلب عينة مجانية</button>
                </div>
            </div>
        </div>
    </div>
</div>
```

### 3. عرض 360 درجة
```html
<!-- 360 View Modal -->
<div class="view-360-modal">
    <div class="modal-content">
        <button class="modal-close">×</button>
        <div class="view-360-container">
            <div class="view-360-viewer">
                <canvas id="product-360-canvas"></canvas>
                <div class="view-360-controls">
                    <button class="control-btn" id="rotate-left">↺</button>
                    <button class="control-btn" id="auto-rotate">⟲</button>
                    <button class="control-btn" id="rotate-right">↻</button>
                </div>
                <div class="view-360-instructions">
                    اسحب للدوران أو استخدم الأزرار
                </div>
            </div>
        </div>
    </div>
</div>
```

### 4. CSS للمنتجات التفاعلية:
```css
/* Products Gallery */
.products-gallery {
    padding: 5rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.2rem;
    color: #666;
}

.products-filter {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid #007bff;
    background: transparent;
    color: #007bff;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background: #007bff;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.3);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    opacity: 1;
    transform: scale(1);
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.product-card.hidden {
    opacity: 0;
    transform: scale(0.8);
    pointer-events: none;
}

.product-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,123,255,0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.product-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.product-actions button {
    padding: 0.5rem 1rem;
    border: 2px solid white;
    background: transparent;
    color: white;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.product-actions button:hover {
    background: white;
    color: #007bff;
}

.product-info {
    padding: 1.5rem;
}

.product-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.product-description {
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.product-specs {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    margin-bottom: 1rem;
}

.spec {
    font-size: 0.9rem;
    color: #888;
    background: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
}

.product-footer {
    display: flex;
    gap: 0.5rem;
}

.btn-quote {
    flex: 1;
    padding: 0.75rem;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.3s ease;
}

.btn-quote:hover {
    background: #0056b3;
}

.btn-sample {
    padding: 0.75rem 1rem;
    background: transparent;
    color: #007bff;
    border: 2px solid #007bff;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-sample:hover {
    background: #007bff;
    color: white;
}

/* Quick View Modal */
.quick-view-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.quick-view-modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: 15px;
    max-width: 900px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.quick-view-modal.active .modal-content {
    transform: scale(1);
}

.modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: #ff4757;
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.5rem;
    z-index: 10001;
}

.modal-body {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    padding: 2rem;
}

.main-image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: 10px;
}

.thumbnail-images {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.thumbnail-images img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 5px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.thumbnail-images img:hover,
.thumbnail-images img.active {
    opacity: 1;
}

.specs-table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

.specs-table td {
    padding: 0.5rem;
    border-bottom: 1px solid #eee;
}

.specs-table td:first-child {
    font-weight: 600;
    color: #333;
}

.product-actions-modal {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 2rem;
}

.product-actions-modal button {
    padding: 0.75rem;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}
```
