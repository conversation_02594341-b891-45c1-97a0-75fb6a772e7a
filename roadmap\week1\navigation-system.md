# نظام التنقل المتقدم - خطة التطوير

## المهام المطلوبة:

### 1. Mega Menu تفاعلي
```html
<!-- Mega Menu Structure -->
<nav class="main-navigation">
    <ul class="nav-menu">
        <li class="nav-item has-mega-menu">
            <a href="#" class="nav-link">المنتجات</a>
            <div class="mega-menu">
                <div class="mega-menu-content">
                    <div class="mega-column">
                        <h4>منتجات الورق</h4>
                        <ul>
                            <li><a href="#">ورق التغليف</a></li>
                            <li><a href="#">ورق الطباعة</a></li>
                            <li><a href="#">ورق الكرتون</a></li>
                        </ul>
                    </div>
                    <div class="mega-column">
                        <h4>منتجات متخصصة</h4>
                        <ul>
                            <li><a href="#">ورق مقاوم للماء</a></li>
                            <li><a href="#">ورق صديق للبيئة</a></li>
                            <li><a href="#">ورق عالي الجودة</a></li>
                        </ul>
                    </div>
                    <div class="mega-column mega-featured">
                        <div class="featured-product">
                            <img src="featured-product.jpg" alt="منتج مميز">
                            <h5>منتج الشهر</h5>
                            <p>ورق عالي الجودة مع تقنية حديثة</p>
                            <a href="#" class="btn-small">اعرف المزيد</a>
                        </div>
                    </div>
                </div>
            </div>
        </li>
        
        <li class="nav-item has-dropdown">
            <a href="#" class="nav-link">الشركة</a>
            <ul class="dropdown-menu">
                <li><a href="#">من نحن</a></li>
                <li><a href="#">رؤيتنا</a></li>
                <li><a href="#">فريق العمل</a></li>
                <li><a href="#">شهادات الجودة</a></li>
            </ul>
        </li>
    </ul>
</nav>
```

### 2. Mobile Menu متطور
```html
<!-- Mobile Menu -->
<div class="mobile-menu-overlay">
    <div class="mobile-menu">
        <div class="mobile-menu-header">
            <img src="logo.png" alt="Logo">
            <button class="close-mobile-menu">×</button>
        </div>
        
        <div class="mobile-menu-content">
            <ul class="mobile-nav">
                <li class="mobile-nav-item">
                    <a href="#" class="mobile-nav-link">الرئيسية</a>
                </li>
                <li class="mobile-nav-item has-submenu">
                    <a href="#" class="mobile-nav-link">
                        المنتجات
                        <span class="submenu-toggle">+</span>
                    </a>
                    <ul class="mobile-submenu">
                        <li><a href="#">ورق التغليف</a></li>
                        <li><a href="#">ورق الطباعة</a></li>
                        <li><a href="#">ورق الكرتون</a></li>
                    </ul>
                </li>
            </ul>
            
            <div class="mobile-menu-footer">
                <div class="contact-info">
                    <p>📞 +20 123 456 789</p>
                    <p>✉️ <EMAIL></p>
                </div>
                <div class="social-links">
                    <a href="#" class="social-link">📘</a>
                    <a href="#" class="social-link">📷</a>
                    <a href="#" class="social-link">🐦</a>
                </div>
            </div>
        </div>
    </div>
</div>
```

### 3. CSS للتنقل المتقدم:
```css
/* Mega Menu Styles */
.mega-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background: white;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-20px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.nav-item:hover .mega-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.mega-menu-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.mega-column h4 {
    color: #333;
    margin-bottom: 1rem;
    font-weight: 600;
    border-bottom: 2px solid #007bff;
    padding-bottom: 0.5rem;
}

.mega-column ul {
    list-style: none;
    padding: 0;
}

.mega-column li {
    margin-bottom: 0.5rem;
}

.mega-column a {
    color: #666;
    text-decoration: none;
    transition: color 0.3s ease;
    display: block;
    padding: 0.5rem 0;
}

.mega-column a:hover {
    color: #007bff;
    transform: translateX(5px);
}

.mega-featured {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
}

.featured-product img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 1rem;
}

/* Mobile Menu Styles */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

.mobile-menu {
    position: absolute;
    top: 0;
    right: -100%;
    width: 320px;
    height: 100%;
    background: white;
    transition: right 0.3s ease;
    overflow-y: auto;
}

.mobile-menu-overlay.active .mobile-menu {
    right: 0;
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #eee;
}

.close-mobile-menu {
    background: none;
    border: none;
    font-size: 2rem;
    cursor: pointer;
    color: #666;
}

.mobile-nav-item {
    border-bottom: 1px solid #f0f0f0;
}

.mobile-nav-link {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    color: #333;
    text-decoration: none;
    transition: background 0.3s ease;
}

.mobile-nav-link:hover {
    background: #f8f9fa;
}

.mobile-submenu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: #f8f9fa;
}

.mobile-submenu.active {
    max-height: 300px;
}

.mobile-submenu a {
    display: block;
    padding: 0.75rem 2rem;
    color: #666;
    text-decoration: none;
    border-bottom: 1px solid #e9ecef;
}

.mobile-menu-footer {
    padding: 2rem 1rem;
    border-top: 1px solid #eee;
    margin-top: auto;
}

.contact-info p {
    margin: 0.5rem 0;
    color: #666;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-link {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: #007bff;
    color: white;
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.social-link:hover {
    transform: scale(1.1);
}
```

### 4. JavaScript للتفاعل:
```javascript
// Mobile Menu Toggle
document.querySelector('.mobile-menu-toggle').addEventListener('click', () => {
    document.querySelector('.mobile-menu-overlay').classList.add('active');
});

document.querySelector('.close-mobile-menu').addEventListener('click', () => {
    document.querySelector('.mobile-menu-overlay').classList.remove('active');
});

// Mobile Submenu Toggle
document.querySelectorAll('.submenu-toggle').forEach(toggle => {
    toggle.addEventListener('click', (e) => {
        e.preventDefault();
        const submenu = toggle.parentElement.nextElementSibling;
        const isActive = submenu.classList.contains('active');
        
        // Close all submenus
        document.querySelectorAll('.mobile-submenu').forEach(sub => {
            sub.classList.remove('active');
        });
        
        // Toggle current submenu
        if (!isActive) {
            submenu.classList.add('active');
            toggle.textContent = '-';
        } else {
            toggle.textContent = '+';
        }
    });
});

// Smooth scroll for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});
```
