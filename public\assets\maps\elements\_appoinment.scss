.appoinment-wrapper-one-start {
    background: #FFFFFF;
    box-shadow: 0px 21px 46px rgba(0, 0, 0, 0.04);
    border-radius: 10px;
    margin-right: -20px;

    @media #{$mdsm-layout} {
        margin-right: 0;
    }
}

.appoinment-wrapper-one-start {
    padding: 73px 100px;

    @media #{$sm-layout} {
        padding: 25px;
        margin-right: 0;
    }

    form {
        input {
            background: #F6F6F6;
            width: 100%;
            height: 55px;
            border: 1px solid transparent;

            &:focus {
                border: 1px solid var(--color-primary) !important;
            }
        }

        textarea {
            background: #F6F6F6;
            height: 150px;
            padding: 15px;
            border: none;
            border: 1px solid transparent !important;

            &:focus {
                border: 1px solid var(--color-primary) !important;
            }
        }

        .single-input {
            margin-bottom: 20px;
        }

        .single-input-wrapper {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 20px;



            .single-input {
                width: 100%;

                input {
                    background: #F6F6F6;
                    width: 100%;
                    height: 55px;
                    border: 1px solid transparent;
                }
            }
        }
    }

}

.appoinment-thumbnail {
    margin-left: -30px;

    @media #{$mdsm-layout} {
        margin-left: 0;
    }
}

.appoinment-area-four-wrapper {
    background-image: url(../images/appoinment/02.webp);
    height: calc(100% - 8px);

    .inner {
        padding: 83px 120px;

        @media #{$smlg-device} {
            padding: 35px;
        }

        @media #{$sm-layout} {
            padding: 25px !important;
            padding-bottom: 60px;
        }

        .title-style-four {
            * {
                color: #fff;
            }
        }
    }

    form {
        max-width: 77%;

        @media #{$smlg-device} {
            max-width: 100%;
        }

        @media #{$mdsm-layout} {
            max-width: 100%;
        }

        .input-group {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 20px;

            .single-input {
                width: 48%;

                @media #{$laptop-device} {
                    width: 100%;
                }

                @media #{$smlg-device} {
                    width: 100%;
                }

                @media #{$mdsm-layout} {
                    width: 100%;
                }
            }

            input {
                width: 100%;
            }
        }

        .single-input {
            margin-bottom: 20px;

            input {
                background: #FFFFFF;
                height: 55px;
            }
        }

        textarea {
            height: 160px;
            background: #FFFFFF;
            padding: 15px;
            margin-bottom: 25px;
        }
    }
}

.appoinment-area-seven {
    background-image: url(../images/appoinment/03.webp);
    background-attachment: fixed;
}

.appoinment-main-wrapper-7 {
    max-width: 712px;
    padding: 61px;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.281);
    margin: auto;

    @media #{$large-mobile} {
        padding: 25px;
    }

    span {
        padding: 3px 12px;
        display: block;
        border-radius: 33px;
        border: 1px solid #E9E9E9;
        max-width: max-content;
        font-size: 16px;
        color: #fff;
    }

    .title {
        font-size: 48px;
        color: #fff;
        margin-bottom: 40px;
        margin-top: 25px;
    }

    .input-half-wrapper {
        display: flex;
        align-items: center;
        gap: 20px;
    }

    .signle-input {
        width: 100%;
        margin-bottom: 20px;
    }

    input {
        height: 55px;
        background: #fff;
        border-radius: 15px;
    }

    textarea {
        height: 150px;
        background: #F6F6F6;
        padding: 15px;
        border-radius: 15px;
    }

    .btn-primary {
        border-radius: 15px;
        margin-top: 30px;
    }
}

.rts-appoinment-area-9 {
    background-image: url(../images/appoinment/04.webp);
}

.appoinment-area-9 {
    background: #fff;
    border-radius: 0 10px 10px 0;
    padding: 70px 53px;
    height: 100%;
    display: flex;
    align-items: center;

    @media #{$large-mobile} {
        padding: 25px;
    }

    .single-input {
        width: 100%;
    }

    input {
        width: 100%;
        margin-bottom: 20px;
        height: 55px;
        border-radius: 15px;
        background: #F6F6F6;
        border: 1px solid transparent;
    }

    textarea {
        height: 150px;
        border-radius: 15px;
        background: #F6F6F6;
        margin-bottom: 30px;
        border: 1px solid transparent;
        padding: 15px;

        &:focus {
            border: 1px solid var(--color-primary);
        }
    }

    .half-input-wrapper {
        display: flex;
        align-items: center;
        gap: 20px;
    }
}

.appoinment-area-accountent {
    background: #fff;
    border-radius: 10px;
    .form-area-right {
        padding: 60px 62px;
        @media #{$large-mobile} {
            padding: 20px;
        }
        .input-wrapper {
            display: flex;
            align-items: center;
            gap: 20px;

            .single-input {
                flex-basis: 50%;
            }
        }

        .single-input {
            .form-select {
                width: 100%;
                height: 55px;
                border-radius: 15px;
                background: #F6F6F6;
                font-size: 16px;
                color: #5D666F;
                border: none;
                padding: 15px;
            }
        }
        label{
            color: #20282D;
            font-size: 18px;
            margin-bottom: 7px;
            margin-top: 25px;
            @media #{$large-mobile} {
                font-size: 14px;
            }
        }
        input {
            height: 55px;
            border-radius: 15px;
            background: #F6F6F6;
            border: none;
            font-size: 16px;
                color: #5D666F;
        }
        textarea{
            border: none;
            height: 150px;
            border-radius: 15px;
            background: #F6F6F6;
            padding: 15px;
            font-size: 16px;
        }
    }

}