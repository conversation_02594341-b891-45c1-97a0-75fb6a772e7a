# تحسين الأداء والسرعة - الأسبوع الثالث

## اليوم 15-17: تحسين السرعة والأداء

### 1. تحسين تحميل الصور (Lazy Loading)
```javascript
// Lazy Loading Implementation
class LazyLoader {
    constructor() {
        this.images = document.querySelectorAll('img[data-src]');
        this.imageObserver = null;
        this.init();
    }
    
    init() {
        if ('IntersectionObserver' in window) {
            this.imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadImage(entry.target);
                        this.imageObserver.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '50px 0px'
            });
            
            this.images.forEach(img => this.imageObserver.observe(img));
        } else {
            // Fallback for older browsers
            this.images.forEach(img => this.loadImage(img));
        }
    }
    
    loadImage(img) {
        img.src = img.dataset.src;
        img.classList.add('loaded');
        img.removeAttribute('data-src');
    }
}

// Initialize lazy loading
document.addEventListener('DOMContentLoaded', () => {
    new LazyLoader();
});
```

### 2. تحسين CSS (Critical CSS)
```css
/* Critical CSS - يتم تحميله أولاً */
.header-modern {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.hero-section {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Non-critical CSS - يتم تحميله لاحقاً */
.products-section,
.about-section,
.services-section {
    /* Styles loaded after critical content */
}
```

### 3. تحسين JavaScript (Code Splitting)
```javascript
// Dynamic imports for better performance
async function loadProductsModule() {
    const { ProductsManager } = await import('./modules/products.js');
    return new ProductsManager();
}

async function loadContactModule() {
    const { ContactForm } = await import('./modules/contact.js');
    return new ContactForm();
}

// Load modules only when needed
document.addEventListener('DOMContentLoaded', async () => {
    // Load critical modules immediately
    const navigation = await import('./modules/navigation.js');
    navigation.init();
    
    // Load other modules on demand
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(async (entry) => {
            if (entry.isIntersecting) {
                const section = entry.target.dataset.section;
                
                switch (section) {
                    case 'products':
                        await loadProductsModule();
                        break;
                    case 'contact':
                        await loadContactModule();
                        break;
                }
                
                observer.unobserve(entry.target);
            }
        });
    });
    
    document.querySelectorAll('[data-section]').forEach(section => {
        observer.observe(section);
    });
});
```

### 4. Service Worker للتخزين المؤقت
```javascript
// service-worker.js
const CACHE_NAME = 'paper-company-v1';
const urlsToCache = [
    '/',
    '/assets/css/style.css',
    '/assets/js/main.js',
    '/assets/images/logo.png'
];

self.addEventListener('install', (event) => {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then((cache) => cache.addAll(urlsToCache))
    );
});

self.addEventListener('fetch', (event) => {
    event.respondWith(
        caches.match(event.request)
            .then((response) => {
                // Return cached version or fetch from network
                return response || fetch(event.request);
            })
    );
});
```

## اليوم 18-19: تحسين SEO

### 1. Meta Tags ديناميكية
```php
// SEO Helper Class
class SEOHelper {
    public static function generateMetaTags($page, $data = []) {
        $defaults = [
            'title' => 'شركة الورق المتقدمة',
            'description' => 'رائدون في صناعة الورق عالي الجودة',
            'keywords' => 'ورق, تصنيع, جودة عالية',
            'image' => asset('images/og-image.jpg')
        ];
        
        $meta = array_merge($defaults, $data);
        
        return view('components.seo-meta', compact('meta'));
    }
}
```

```html
<!-- components/seo-meta.blade.php -->
<title>{{ $meta['title'] }}</title>
<meta name="description" content="{{ $meta['description'] }}">
<meta name="keywords" content="{{ $meta['keywords'] }}">

<!-- Open Graph -->
<meta property="og:title" content="{{ $meta['title'] }}">
<meta property="og:description" content="{{ $meta['description'] }}">
<meta property="og:image" content="{{ $meta['image'] }}">
<meta property="og:url" content="{{ url()->current() }}">
<meta property="og:type" content="website">

<!-- Twitter Card -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="{{ $meta['title'] }}">
<meta name="twitter:description" content="{{ $meta['description'] }}">
<meta name="twitter:image" content="{{ $meta['image'] }}">

<!-- Schema.org -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "{{ $meta['title'] }}",
    "description": "{{ $meta['description'] }}",
    "url": "{{ url('/') }}",
    "logo": "{{ asset('images/logo.png') }}",
    "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "+20-123-456-789",
        "contactType": "customer service"
    }
}
</script>
```

### 2. Sitemap ديناميكي
```php
// SitemapController.php
class SitemapController extends Controller {
    public function index() {
        $urls = [
            ['url' => '/', 'priority' => '1.0', 'changefreq' => 'daily'],
            ['url' => '/products', 'priority' => '0.9', 'changefreq' => 'weekly'],
            ['url' => '/about', 'priority' => '0.8', 'changefreq' => 'monthly'],
            ['url' => '/contact', 'priority' => '0.7', 'changefreq' => 'monthly'],
        ];
        
        // Add dynamic product URLs
        $products = Product::all();
        foreach ($products as $product) {
            $urls[] = [
                'url' => '/products/' . $product->slug,
                'priority' => '0.8',
                'changefreq' => 'weekly'
            ];
        }
        
        return response()->view('sitemap', compact('urls'))
                        ->header('Content-Type', 'text/xml');
    }
}
```

## اليوم 20-21: تحسين تجربة المستخدم

### 1. Loading States متقدمة
```css
/* Loading Animations */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.skeleton-text {
    height: 1rem;
    margin: 0.5rem 0;
    border-radius: 4px;
}

.skeleton-image {
    height: 200px;
    border-radius: 8px;
}

.skeleton-button {
    height: 40px;
    width: 120px;
    border-radius: 20px;
}
```

```javascript
// Loading State Manager
class LoadingManager {
    static showSkeleton(container) {
        const skeleton = `
            <div class="skeleton-container">
                <div class="skeleton skeleton-image"></div>
                <div class="skeleton skeleton-text"></div>
                <div class="skeleton skeleton-text" style="width: 80%"></div>
                <div class="skeleton skeleton-button"></div>
            </div>
        `;
        container.innerHTML = skeleton;
    }
    
    static hideSkeleton(container, content) {
        container.innerHTML = content;
    }
    
    static showSpinner(element) {
        element.classList.add('loading');
        element.innerHTML = '<div class="spinner"></div>';
    }
    
    static hideSpinner(element, originalContent) {
        element.classList.remove('loading');
        element.innerHTML = originalContent;
    }
}
```

### 2. Error Handling متقدم
```javascript
// Error Handler
class ErrorHandler {
    static handleNetworkError(error) {
        console.error('Network Error:', error);
        this.showNotification('خطأ في الاتصال، يرجى المحاولة مرة أخرى', 'error');
    }
    
    static handleValidationError(errors) {
        Object.keys(errors).forEach(field => {
            const input = document.querySelector(`[name="${field}"]`);
            if (input) {
                this.showFieldError(input, errors[field][0]);
            }
        });
    }
    
    static showFieldError(input, message) {
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error';
        errorElement.textContent = message;
        
        input.classList.add('error');
        input.parentNode.appendChild(errorElement);
        
        setTimeout(() => {
            input.classList.remove('error');
            if (errorElement.parentNode) {
                errorElement.parentNode.removeChild(errorElement);
            }
        }, 5000);
    }
    
    static showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => notification.classList.add('show'), 100);
        
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.classList.remove('show');
            setTimeout(() => document.body.removeChild(notification), 300);
        });
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }
        }, 5000);
    }
}
```

## الملفات المطلوبة للأسبوع الثالث:

```
public/assets/js/modules/
├── lazy-loading.js
├── performance.js
├── seo-helper.js
├── loading-manager.js
└── error-handler.js

public/
├── service-worker.js
└── manifest.json

app/Http/Controllers/
└── SitemapController.php

resources/views/
├── sitemap.blade.php
└── components/seo-meta.blade.php
```
