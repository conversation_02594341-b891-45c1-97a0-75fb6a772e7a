<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض توضيحي لتنسيقات التنقل</title>
    <link rel="stylesheet" href="assets/css/custom-navigation.css">
    <link rel="stylesheet" href="assets/css/navigation-examples.css">
    <style>
        :root {
            --color-primary: #4a6cf7;
            --color-primary-rgb: 74, 108, 247;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .demo-section {
            margin: 40px 0;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .demo-title {
            color: #333;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 20px;
            text-align: center;
            border-bottom: 3px solid var(--color-primary);
            padding-bottom: 10px;
        }
        
        .demo-description {
            color: #666;
            font-size: 16px;
            margin-bottom: 25px;
            text-align: center;
            line-height: 1.6;
        }
        
        .nav-demo {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
        }
        
        .submenu-demo {
            max-width: 300px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        /* إعادة تعيين الأنماط الافتراضية */
        ul {
            list-style: none;
            margin: 0;
            padding: 0;
        }
        
        a {
            text-decoration: none;
            color: inherit;
        }
        
        /* تنسيقات متجاوبة */
        @media (max-width: 768px) {
            .demo-section {
                margin: 20px 0;
                padding: 20px;
            }
            
            .demo-title {
                font-size: 20px;
            }
            
            .demo-description {
                font-size: 14px;
            }
            
            .nav-demo {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: #333; font-size: 32px; margin-bottom: 40px;">
            عرض توضيحي لتنسيقات التنقل بدون استخدام ::after و ::before
        </h1>
        
        <!-- المثال الأول: التنسيق الأساسي -->
        <div class="demo-section">
            <h2 class="demo-title">المثال الأول: التنسيق الأساسي المحسن</h2>
            <p class="demo-description">
                تنسيق بسيط مع تأثيرات لونية وحركية جميلة
            </p>
            <nav class="nav-style-1">
                <ul class="nav-demo">
                    <li><a href="#">الرئيسية</a></li>
                    <li><a href="#">من نحن</a></li>
                    <li><a href="#">المنتجات</a></li>
                    <li><a href="#">الأخبار</a></li>
                    <li><a href="#">اتصل بنا</a></li>
                </ul>
            </nav>
        </div>
        
        <!-- المثال الثاني: خلفية متدرجة -->
        <div class="demo-section">
            <h2 class="demo-title">المثال الثاني: تنسيق بخلفية متدرجة</h2>
            <p class="demo-description">
                تنسيق عصري مع خلفيات متدرجة وتأثيرات دوران
            </p>
            <nav class="nav-style-2">
                <ul class="nav-demo">
                    <li><a href="#">HOME</a></li>
                    <li><a href="#">ABOUT</a></li>
                    <li><a href="#">SERVICES</a></li>
                    <li><a href="#">CONTACT</a></li>
                </ul>
            </nav>
        </div>
        
        <!-- المثال الثالث: حدود ملونة -->
        <div class="demo-section">
            <h2 class="demo-title">المثال الثالث: تنسيق بحدود ملونة</h2>
            <p class="demo-description">
                تنسيق مميز مع حدود ملونة وتأثيرات انحراف
            </p>
            <nav class="nav-style-3">
                <ul class="nav-demo">
                    <li><a href="#">الرئيسية</a></li>
                    <li><a href="#">الخدمات</a></li>
                    <li><a href="#">المشاريع</a></li>
                    <li><a href="#">المدونة</a></li>
                    <li><a href="#">تواصل معنا</a></li>
                </ul>
            </nav>
        </div>
        
        <!-- المثال الرابع: ظلال متقدمة -->
        <div class="demo-section">
            <h2 class="demo-title">المثال الرابع: تنسيق بظلال متقدمة</h2>
            <p class="demo-description">
                تنسيق أنيق مع ظلال متدرجة وتأثيرات رفع
            </p>
            <nav class="nav-style-4">
                <ul class="nav-demo">
                    <li><a href="#">الصفحة الرئيسية</a></li>
                    <li><a href="#">معلومات عنا</a></li>
                    <li><a href="#">منتجاتنا</a></li>
                    <li><a href="#">أخبارنا</a></li>
                    <li><a href="#">اتصل بنا</a></li>
                </ul>
            </nav>
        </div>
        
        <!-- المثال الخامس: تأثيرات نيون -->
        <div class="demo-section" style="background: #1a1a1a;">
            <h2 class="demo-title" style="color: #00ff88;">المثال الخامس: تنسيق بتأثيرات نيون</h2>
            <p class="demo-description" style="color: #ccc;">
                تنسيق مستقبلي مع تأثيرات نيون مضيئة
            </p>
            <nav class="nav-style-5">
                <ul class="nav-demo">
                    <li><a href="#">HOME</a></li>
                    <li><a href="#">TECH</a></li>
                    <li><a href="#">GAMING</a></li>
                    <li><a href="#">NEWS</a></li>
                    <li><a href="#">CONTACT</a></li>
                </ul>
            </nav>
        </div>
        
        <!-- المثال السادس: تأثيرات ثلاثية الأبعاد -->
        <div class="demo-section">
            <h2 class="demo-title">المثال السادس: تنسيق ثلاثي الأبعاد</h2>
            <p class="demo-description">
                تنسيق مع تأثيرات ثلاثية الأبعاد وظلال محفورة
            </p>
            <nav class="nav-style-6">
                <ul class="nav-demo">
                    <li><a href="#">الرئيسية</a></li>
                    <li><a href="#">التصميم</a></li>
                    <li><a href="#">التطوير</a></li>
                    <li><a href="#">الدعم</a></li>
                </ul>
            </nav>
        </div>
        
        <!-- المثال السابع: تأثيرات الموجات -->
        <div class="demo-section">
            <h2 class="demo-title">المثال السابع: تنسيق بتأثيرات الموجات</h2>
            <p class="demo-description">
                تنسيق ديناميكي مع تأثيرات موجية متحركة
            </p>
            <nav class="nav-style-7">
                <ul class="nav-demo">
                    <li><a href="#">الرئيسية</a></li>
                    <li><a href="#">الإبداع</a></li>
                    <li><a href="#">الابتكار</a></li>
                    <li><a href="#">التميز</a></li>
                </ul>
            </nav>
        </div>
        
        <!-- مثال للقائمة الفرعية -->
        <div class="demo-section">
            <h2 class="demo-title">مثال للقائمة الفرعية</h2>
            <p class="demo-description">
                تنسيق خاص للقوائم الفرعية مع تأثيرات انزلاق
            </p>
            <div class="submenu-demo">
                <ul class="submenu-style-1">
                    <li><a href="#">تاريخ الشركة</a></li>
                    <li><a href="#">الرؤية والرسالة</a></li>
                    <li><a href="#">فريق العمل</a></li>
                    <li><a href="#">شهادات الجودة</a></li>
                    <li><a href="#">المسؤولية الاجتماعية</a></li>
                    <li><a href="#">الجوائز والتقدير</a></li>
                </ul>
            </div>
        </div>
        
        <!-- ملاحظات مهمة -->
        <div class="demo-section" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <h2 class="demo-title" style="color: white; border-color: white;">ملاحظات مهمة</h2>
            <div style="text-align: right; line-height: 1.8; font-size: 16px;">
                <p><strong>✓</strong> جميع التنسيقات المعروضة تطبق على عناصر li بدون استخدام ::after أو ::before</p>
                <p><strong>✓</strong> التنسيقات متجاوبة وتعمل على جميع الأجهزة</p>
                <p><strong>✓</strong> يمكن دمج عدة أنماط معاً لإنشاء تصميم فريد</p>
                <p><strong>✓</strong> جميع التأثيرات محسنة للأداء باستخدام CSS3</p>
                <p><strong>✓</strong> التنسيقات تدعم الوضع المظلم والفاتح</p>
            </div>
        </div>
    </div>
</body>
</html>
