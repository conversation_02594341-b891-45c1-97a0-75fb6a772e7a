# الاختبار والتحسين - الأسبوع الخامس

## اليوم 29-31: اختبار الأداء والجودة

### 1. اختبار الأداء (Performance Testing)
```javascript
// Performance Testing Suite
class PerformanceTester {
    constructor() {
        this.metrics = {};
        this.init();
    }
    
    init() {
        this.measurePageLoad();
        this.measureInteractivity();
        this.measureVisualStability();
        this.generateReport();
    }
    
    measurePageLoad() {
        // Measure Core Web Vitals
        new PerformanceObserver((entryList) => {
            for (const entry of entryList.getEntries()) {
                if (entry.entryType === 'largest-contentful-paint') {
                    this.metrics.LCP = entry.startTime;
                }
                if (entry.entryType === 'first-input') {
                    this.metrics.FID = entry.processingStart - entry.startTime;
                }
            }
        }).observe({entryTypes: ['largest-contentful-paint', 'first-input']});
        
        // Measure Cumulative Layout Shift
        let clsValue = 0;
        new PerformanceObserver((entryList) => {
            for (const entry of entryList.getEntries()) {
                if (!entry.hadRecentInput) {
                    clsValue += entry.value;
                }
            }
            this.metrics.CLS = clsValue;
        }).observe({entryTypes: ['layout-shift']});
        
        // Measure Time to First Byte
        new PerformanceObserver((entryList) => {
            const [pageNav] = entryList.getEntries();
            this.metrics.TTFB = pageNav.responseStart - pageNav.requestStart;
        }).observe({entryTypes: ['navigation']});
    }
    
    measureInteractivity() {
        // Measure button response times
        document.addEventListener('click', (e) => {
            if (e.target.matches('button, .btn')) {
                const startTime = performance.now();
                
                requestAnimationFrame(() => {
                    const endTime = performance.now();
                    const responseTime = endTime - startTime;
                    
                    if (!this.metrics.buttonResponses) {
                        this.metrics.buttonResponses = [];
                    }
                    this.metrics.buttonResponses.push(responseTime);
                });
            }
        });
    }
    
    measureVisualStability() {
        // Track image loading impact
        const images = document.querySelectorAll('img');
        let imagesLoaded = 0;
        
        images.forEach(img => {
            if (img.complete) {
                imagesLoaded++;
            } else {
                img.addEventListener('load', () => {
                    imagesLoaded++;
                    this.metrics.imageLoadProgress = (imagesLoaded / images.length) * 100;
                });
            }
        });
    }
    
    generateReport() {
        setTimeout(() => {
            const report = {
                timestamp: new Date().toISOString(),
                url: window.location.href,
                metrics: this.metrics,
                recommendations: this.getRecommendations()
            };
            
            this.sendReport(report);
            console.log('Performance Report:', report);
        }, 5000);
    }
    
    getRecommendations() {
        const recommendations = [];
        
        if (this.metrics.LCP > 2500) {
            recommendations.push('تحسين Largest Contentful Paint - قم بتحسين تحميل الصور الرئيسية');
        }
        
        if (this.metrics.FID > 100) {
            recommendations.push('تحسين First Input Delay - قلل من JavaScript المعطل');
        }
        
        if (this.metrics.CLS > 0.1) {
            recommendations.push('تحسين Cumulative Layout Shift - حدد أبعاد الصور والعناصر');
        }
        
        if (this.metrics.TTFB > 600) {
            recommendations.push('تحسين Time to First Byte - حسن أداء الخادم');
        }
        
        return recommendations;
    }
    
    sendReport(report) {
        fetch('/performance/report', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify(report)
        });
    }
}

// Initialize performance testing
if (window.location.search.includes('performance-test=true')) {
    new PerformanceTester();
}
```

### 2. اختبار التوافق (Cross-browser Testing)
```javascript
// Browser Compatibility Checker
class CompatibilityChecker {
    constructor() {
        this.features = [
            'IntersectionObserver',
            'fetch',
            'Promise',
            'localStorage',
            'sessionStorage',
            'WebGL',
            'ServiceWorker',
            'PushManager'
        ];
        
        this.cssFeatures = [
            'grid',
            'flexbox',
            'transforms',
            'transitions',
            'animations'
        ];
        
        this.checkCompatibility();
    }
    
    checkCompatibility() {
        const results = {
            browser: this.getBrowserInfo(),
            jsFeatures: this.checkJSFeatures(),
            cssFeatures: this.checkCSSFeatures(),
            recommendations: []
        };
        
        this.generateRecommendations(results);
        this.sendCompatibilityReport(results);
    }
    
    getBrowserInfo() {
        const ua = navigator.userAgent;
        let browser = 'Unknown';
        let version = 'Unknown';
        
        if (ua.includes('Chrome')) {
            browser = 'Chrome';
            version = ua.match(/Chrome\/(\d+)/)?.[1];
        } else if (ua.includes('Firefox')) {
            browser = 'Firefox';
            version = ua.match(/Firefox\/(\d+)/)?.[1];
        } else if (ua.includes('Safari') && !ua.includes('Chrome')) {
            browser = 'Safari';
            version = ua.match(/Version\/(\d+)/)?.[1];
        } else if (ua.includes('Edge')) {
            browser = 'Edge';
            version = ua.match(/Edge\/(\d+)/)?.[1];
        }
        
        return { browser, version, userAgent: ua };
    }
    
    checkJSFeatures() {
        const results = {};
        
        this.features.forEach(feature => {
            switch (feature) {
                case 'IntersectionObserver':
                    results[feature] = 'IntersectionObserver' in window;
                    break;
                case 'fetch':
                    results[feature] = 'fetch' in window;
                    break;
                case 'Promise':
                    results[feature] = 'Promise' in window;
                    break;
                case 'localStorage':
                    results[feature] = 'localStorage' in window;
                    break;
                case 'sessionStorage':
                    results[feature] = 'sessionStorage' in window;
                    break;
                case 'WebGL':
                    results[feature] = this.checkWebGL();
                    break;
                case 'ServiceWorker':
                    results[feature] = 'serviceWorker' in navigator;
                    break;
                case 'PushManager':
                    results[feature] = 'PushManager' in window;
                    break;
            }
        });
        
        return results;
    }
    
    checkCSSFeatures() {
        const results = {};
        
        this.cssFeatures.forEach(feature => {
            results[feature] = CSS.supports(this.getCSSTestProperty(feature));
        });
        
        return results;
    }
    
    getCSSTestProperty(feature) {
        const tests = {
            'grid': 'display: grid',
            'flexbox': 'display: flex',
            'transforms': 'transform: translateX(0)',
            'transitions': 'transition: opacity 1s',
            'animations': 'animation: none'
        };
        
        return tests[feature] || '';
    }
    
    checkWebGL() {
        try {
            const canvas = document.createElement('canvas');
            return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
        } catch (e) {
            return false;
        }
    }
    
    generateRecommendations(results) {
        if (!results.jsFeatures.IntersectionObserver) {
            results.recommendations.push('استخدم polyfill لـ IntersectionObserver');
        }
        
        if (!results.jsFeatures.fetch) {
            results.recommendations.push('استخدم polyfill لـ fetch API');
        }
        
        if (!results.cssFeatures.grid) {
            results.recommendations.push('استخدم fallback لـ CSS Grid');
        }
        
        if (!results.cssFeatures.flexbox) {
            results.recommendations.push('استخدم fallback لـ Flexbox');
        }
    }
    
    sendCompatibilityReport(results) {
        fetch('/compatibility/report', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify(results)
        });
    }
}

// Run compatibility check
new CompatibilityChecker();
```

### 3. اختبار إمكانية الوصول (Accessibility Testing)
```javascript
// Accessibility Checker
class AccessibilityChecker {
    constructor() {
        this.issues = [];
        this.runChecks();
    }
    
    runChecks() {
        this.checkImages();
        this.checkHeadings();
        this.checkForms();
        this.checkColors();
        this.checkKeyboardNavigation();
        this.generateReport();
    }
    
    checkImages() {
        const images = document.querySelectorAll('img');
        
        images.forEach((img, index) => {
            if (!img.alt) {
                this.issues.push({
                    type: 'missing-alt',
                    element: 'img',
                    message: `صورة بدون نص بديل (alt) - العنصر رقم ${index + 1}`,
                    severity: 'high',
                    element_info: this.getElementInfo(img)
                });
            }
            
            if (img.alt && img.alt.length > 125) {
                this.issues.push({
                    type: 'long-alt',
                    element: 'img',
                    message: `نص بديل طويل جداً (${img.alt.length} حرف)`,
                    severity: 'medium',
                    element_info: this.getElementInfo(img)
                });
            }
        });
    }
    
    checkHeadings() {
        const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
        let previousLevel = 0;
        
        headings.forEach((heading, index) => {
            const currentLevel = parseInt(heading.tagName.charAt(1));
            
            if (index === 0 && currentLevel !== 1) {
                this.issues.push({
                    type: 'heading-order',
                    element: heading.tagName,
                    message: 'يجب أن يبدأ المحتوى بـ H1',
                    severity: 'high',
                    element_info: this.getElementInfo(heading)
                });
            }
            
            if (currentLevel - previousLevel > 1) {
                this.issues.push({
                    type: 'heading-skip',
                    element: heading.tagName,
                    message: `تخطي مستوى العنوان من H${previousLevel} إلى H${currentLevel}`,
                    severity: 'medium',
                    element_info: this.getElementInfo(heading)
                });
            }
            
            previousLevel = currentLevel;
        });
    }
    
    checkForms() {
        const inputs = document.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            const label = document.querySelector(`label[for="${input.id}"]`);
            const ariaLabel = input.getAttribute('aria-label');
            const ariaLabelledby = input.getAttribute('aria-labelledby');
            
            if (!label && !ariaLabel && !ariaLabelledby) {
                this.issues.push({
                    type: 'missing-label',
                    element: input.tagName,
                    message: 'حقل إدخال بدون تسمية (label)',
                    severity: 'high',
                    element_info: this.getElementInfo(input)
                });
            }
        });
        
        const buttons = document.querySelectorAll('button');
        buttons.forEach(button => {
            if (!button.textContent.trim() && !button.getAttribute('aria-label')) {
                this.issues.push({
                    type: 'empty-button',
                    element: 'button',
                    message: 'زر بدون نص أو تسمية',
                    severity: 'high',
                    element_info: this.getElementInfo(button)
                });
            }
        });
    }
    
    checkColors() {
        // Simple contrast check (would need more sophisticated implementation)
        const elements = document.querySelectorAll('*');
        
        elements.forEach(element => {
            const styles = window.getComputedStyle(element);
            const color = styles.color;
            const backgroundColor = styles.backgroundColor;
            
            // This is a simplified check - real implementation would calculate contrast ratio
            if (color === backgroundColor) {
                this.issues.push({
                    type: 'poor-contrast',
                    element: element.tagName,
                    message: 'تباين ضعيف بين النص والخلفية',
                    severity: 'medium',
                    element_info: this.getElementInfo(element)
                });
            }
        });
    }
    
    checkKeyboardNavigation() {
        const focusableElements = document.querySelectorAll(
            'a, button, input, textarea, select, [tabindex]:not([tabindex="-1"])'
        );
        
        focusableElements.forEach(element => {
            if (element.tabIndex < 0 && element.tabIndex !== -1) {
                this.issues.push({
                    type: 'invalid-tabindex',
                    element: element.tagName,
                    message: `قيمة tabindex غير صحيحة: ${element.tabIndex}`,
                    severity: 'medium',
                    element_info: this.getElementInfo(element)
                });
            }
        });
    }
    
    getElementInfo(element) {
        return {
            tagName: element.tagName,
            id: element.id,
            className: element.className,
            textContent: element.textContent?.substring(0, 50) + '...'
        };
    }
    
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            url: window.location.href,
            total_issues: this.issues.length,
            issues_by_severity: {
                high: this.issues.filter(i => i.severity === 'high').length,
                medium: this.issues.filter(i => i.severity === 'medium').length,
                low: this.issues.filter(i => i.severity === 'low').length
            },
            issues: this.issues,
            score: this.calculateScore()
        };
        
        this.sendAccessibilityReport(report);
        console.log('Accessibility Report:', report);
    }
    
    calculateScore() {
        const totalElements = document.querySelectorAll('*').length;
        const issueWeight = {
            high: 3,
            medium: 2,
            low: 1
        };
        
        const totalWeight = this.issues.reduce((sum, issue) => {
            return sum + issueWeight[issue.severity];
        }, 0);
        
        return Math.max(0, 100 - (totalWeight / totalElements * 100));
    }
    
    sendAccessibilityReport(report) {
        fetch('/accessibility/report', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify(report)
        });
    }
}

// Run accessibility check
if (window.location.search.includes('accessibility-test=true')) {
    new AccessibilityChecker();
}
```

## اليوم 32-35: تحسين الأداء النهائي

### 1. تحسين قاعدة البيانات
```php
// Database Optimization
class DatabaseOptimizer {
    public function optimizeQueries() {
        // Add indexes for frequently queried columns
        Schema::table('products', function (Blueprint $table) {
            $table->index(['category', 'status']);
            $table->index('created_at');
            $table->fullText(['name', 'description']);
        });
        
        Schema::table('analytics_events', function (Blueprint $table) {
            $table->index(['event_type', 'created_at']);
            $table->index('session_id');
        });
    }
    
    public function cacheFrequentQueries() {
        // Cache popular products
        Cache::remember('popular_products', 3600, function () {
            return Product::withCount('views')
                         ->orderBy('views_count', 'desc')
                         ->limit(10)
                         ->get();
        });
        
        // Cache categories
        Cache::remember('product_categories', 7200, function () {
            return Product::select('category')
                         ->distinct()
                         ->pluck('category');
        });
    }
}
```

### 2. تحسين الصور
```javascript
// Image Optimization
class ImageOptimizer {
    constructor() {
        this.setupWebPSupport();
        this.setupResponsiveImages();
        this.setupImageCompression();
    }
    
    setupWebPSupport() {
        // Check WebP support
        const webpSupported = this.checkWebPSupport();
        
        if (webpSupported) {
            document.documentElement.classList.add('webp-supported');
        }
    }
    
    checkWebPSupport() {
        return new Promise((resolve) => {
            const webP = new Image();
            webP.onload = webP.onerror = () => {
                resolve(webP.height === 2);
            };
            webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
        });
    }
    
    setupResponsiveImages() {
        const images = document.querySelectorAll('img[data-responsive]');
        
        images.forEach(img => {
            const sizes = img.dataset.sizes?.split(',') || ['400', '800', '1200'];
            const srcset = sizes.map(size => 
                `${img.dataset.src}?w=${size} ${size}w`
            ).join(', ');
            
            img.srcset = srcset;
            img.sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw';
        });
    }
    
    setupImageCompression() {
        // Compress images before upload
        const fileInputs = document.querySelectorAll('input[type="file"][accept*="image"]');
        
        fileInputs.forEach(input => {
            input.addEventListener('change', (e) => {
                const files = Array.from(e.target.files);
                const compressedFiles = [];
                
                files.forEach(file => {
                    if (file.type.startsWith('image/')) {
                        this.compressImage(file).then(compressedFile => {
                            compressedFiles.push(compressedFile);
                        });
                    }
                });
            });
        });
    }
    
    compressImage(file, quality = 0.8) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            img.onload = () => {
                const maxWidth = 1920;
                const maxHeight = 1080;
                
                let { width, height } = img;
                
                if (width > maxWidth) {
                    height = (height * maxWidth) / width;
                    width = maxWidth;
                }
                
                if (height > maxHeight) {
                    width = (width * maxHeight) / height;
                    height = maxHeight;
                }
                
                canvas.width = width;
                canvas.height = height;
                
                ctx.drawImage(img, 0, 0, width, height);
                
                canvas.toBlob(resolve, file.type, quality);
            };
            
            img.src = URL.createObjectURL(file);
        });
    }
}

// Initialize image optimization
new ImageOptimizer();
```

## الملفات المطلوبة للأسبوع الخامس:

```
public/assets/js/testing/
├── performance-tester.js
├── compatibility-checker.js
├── accessibility-checker.js
└── image-optimizer.js

app/Http/Controllers/
├── PerformanceController.php
├── CompatibilityController.php
└── AccessibilityController.php

database/migrations/
├── add_indexes_to_products_table.php
└── add_indexes_to_analytics_events_table.php

tests/Feature/
├── PerformanceTest.php
├── AccessibilityTest.php
└── CompatibilityTest.php
```
