# Hero Section مذهل - خطة التطوير

## المهام المطلوبة:

### 1. تصميم Hero Section
```html
<!-- Hero Structure -->
<section class="hero-section">
    <div class="hero-background">
        <video autoplay muted loop>
            <source src="factory-video.mp4" type="video/mp4">
        </video>
        <div class="hero-overlay"></div>
    </div>
    
    <div class="hero-content">
        <div class="container">
            <div class="hero-text">
                <h1 class="hero-title animated">
                    <span class="highlight">ورق</span> عالي الجودة
                    <br>لمستقبل مستدام
                </h1>
                <p class="hero-description">
                    نحن رواد صناعة الورق في المنطقة مع أحدث التقنيات
                </p>
                <div class="hero-buttons">
                    <a href="#products" class="btn-primary">منتجاتنا</a>
                    <a href="#about" class="btn-secondary">من نحن</a>
                </div>
            </div>
            
            <div class="hero-stats">
                <div class="stat-item">
                    <span class="stat-number" data-count="15">0</span>
                    <span class="stat-label">سنة خبرة</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" data-count="500">0</span>
                    <span class="stat-label">عميل راضي</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" data-count="50">0</span>
                    <span class="stat-label">دولة نصدر إليها</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="hero-scroll-indicator">
        <div class="scroll-arrow"></div>
    </div>
</section>
```

### 2. ميزات متقدمة:
- ✅ فيديو خلفية عالي الجودة
- ✅ نص متحرك مع تأثيرات
- ✅ إحصائيات متحركة (counter animation)
- ✅ أزرار تفاعلية
- ✅ مؤشر التمرير

### 3. CSS المطلوب:
```css
.hero-section {
    height: 100vh;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
}

.hero-background video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -2;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        45deg,
        rgba(0,123,255,0.8),
        rgba(0,198,255,0.6)
    );
    z-index: -1;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    color: white;
    margin-bottom: 2rem;
    opacity: 0;
    transform: translateY(50px);
    animation: fadeInUp 1s ease forwards;
}

.highlight {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-stats {
    display: flex;
    gap: 3rem;
    margin-top: 3rem;
}

.stat-item {
    text-align: center;
    color: white;
}

.stat-number {
    display: block;
    font-size: 3rem;
    font-weight: 700;
    color: #FFD700;
}

.scroll-arrow {
    width: 30px;
    height: 30px;
    border: 2px solid white;
    border-top: none;
    border-right: none;
    transform: rotate(-45deg);
    animation: bounce 2s infinite;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0) rotate(-45deg);
    }
    40% {
        transform: translateY(-10px) rotate(-45deg);
    }
    60% {
        transform: translateY(-5px) rotate(-45deg);
    }
}
```

### 4. JavaScript للإحصائيات المتحركة:
```javascript
// Counter Animation
function animateCounters() {
    const counters = document.querySelectorAll('.stat-number');
    
    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-count'));
        const duration = 2000; // 2 seconds
        const step = target / (duration / 16); // 60fps
        let current = 0;
        
        const timer = setInterval(() => {
            current += step;
            counter.textContent = Math.floor(current);
            
            if (current >= target) {
                counter.textContent = target;
                clearInterval(timer);
            }
        }, 16);
    });
}

// Trigger animation when hero section is visible
const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            animateCounters();
            observer.unobserve(entry.target);
        }
    });
});

observer.observe(document.querySelector('.hero-section'));
```

### 5. الملفات المطلوبة:
```
resources/views/sections/
└── hero-modern.blade.php

public/assets/css/
└── hero-section.css

public/assets/js/
└── hero-animations.js

public/assets/videos/
├── factory-overview.mp4
├── production-line.mp4
└── quality-control.mp4
```
