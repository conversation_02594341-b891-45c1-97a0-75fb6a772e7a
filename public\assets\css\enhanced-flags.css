/* أيقونات أعلام محسنة مع صور عالية الجودة */

/* تحسين العلم المصري */
.flag-icon-eg {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 900 600'%3E%3Cdefs%3E%3ClinearGradient id='redGrad' x1='0%25' y1='0%25' x2='100%25' y2='0%25'%3E%3Cstop offset='0%25' style='stop-color:%23ce1126;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23b71c1c;stop-opacity:1' /%3E%3C/linearGradient%3E%3ClinearGradient id='whiteGrad' x1='0%25' y1='0%25' x2='100%25' y2='0%25'%3E%3Cstop offset='0%25' style='stop-color:%23ffffff;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23f5f5f5;stop-opacity:1' /%3E%3C/linearGradient%3E%3ClinearGradient id='blackGrad' x1='0%25' y1='0%25' x2='100%25' y2='0%25'%3E%3Cstop offset='0%25' style='stop-color:%23000000;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23212121;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Cpath fill='url(%23redGrad)' d='M0 0h900v200H0z'/%3E%3Cpath fill='url(%23whiteGrad)' d='M0 200h900v200H0z'/%3E%3Cpath fill='url(%23blackGrad)' d='M0 400h900v200H0z'/%3E%3Cg fill='%23ffd700' stroke='%23b8860b' stroke-width='2'%3E%3Ccircle cx='450' cy='300' r='50'/%3E%3Cpath d='M450 250l15 30h30l-25 20 10 30-30-20-30 20 10-30-25-20h30z'/%3E%3C/g%3E%3C/svg%3E");
}

/* تحسين العلم البريطاني */
.flag-icon-gb {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 60 30'%3E%3Cdefs%3E%3ClinearGradient id='blueGrad' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%23012169;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23001a5c;stop-opacity:1' /%3E%3C/linearGradient%3E%3ClinearGradient id='redGrad' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%23C8102E;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23a00d26;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Cclippath id='a'%3E%3Cpath d='M0 0v30h60V0z'/%3E%3C/clippath%3E%3Cclippath id='b'%3E%3Cpath d='M30 15h30v15zv15H0zH0V0zV0h30z'/%3E%3C/clippath%3E%3Cg clip-path='url(%23a)'%3E%3Cpath d='M0 0v30h60V0z' fill='url(%23blueGrad)'/%3E%3Cpath d='M0 0l60 30m0-30L0 30' stroke='%23fff' stroke-width='6'/%3E%3Cpath d='M0 0l60 30m0-30L0 30' clip-path='url(%23b)' stroke='url(%23redGrad)' stroke-width='4'/%3E%3Cpath d='M30 0v30M0 15h60' stroke='%23fff' stroke-width='10'/%3E%3Cpath d='M30 0v30M0 15h60' stroke='url(%23redGrad)' stroke-width='6'/%3E%3C/g%3E%3C/svg%3E");
}

/* العلم السعودي */
.flag-icon-sa {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 900 600'%3E%3Cdefs%3E%3ClinearGradient id='greenGrad' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%23006C35;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23004d26;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Cpath fill='url(%23greenGrad)' d='M0 0h900v600H0z'/%3E%3Cg fill='%23fff' font-family='Arial' font-size='48' text-anchor='middle'%3E%3Ctext x='450' y='280'%3Eلا إله إلا الله محمد رسول الله%3C/text%3E%3Cpath d='M350 350h200v20h-200z'/%3E%3Cpath d='M400 320h100l-20 30h-60z'/%3E%3C/g%3E%3C/svg%3E");
}

/* العلم الإماراتي */
.flag-icon-ae {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 600'%3E%3Cdefs%3E%3ClinearGradient id='redGrad' x1='0%25' y1='0%25' x2='100%25' y2='0%25'%3E%3Cstop offset='0%25' style='stop-color:%23FF0000;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23cc0000;stop-opacity:1' /%3E%3C/linearGradient%3E%3ClinearGradient id='greenGrad' x1='0%25' y1='0%25' x2='100%25' y2='0%25'%3E%3Cstop offset='0%25' style='stop-color:%23009639;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23007a2f;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Cpath fill='url(%23redGrad)' d='M0 0h400v600H0z'/%3E%3Cpath fill='url(%23greenGrad)' d='M400 0h800v200H400z'/%3E%3Cpath fill='%23fff' d='M400 200h800v200H400z'/%3E%3Cpath fill='%23000' d='M400 400h800v200H400z'/%3E%3C/svg%3E");
}

/* العلم الكويتي */
.flag-icon-kw {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 600'%3E%3Cdefs%3E%3ClinearGradient id='greenGrad' x1='0%25' y1='0%25' x2='100%25' y2='0%25'%3E%3Cstop offset='0%25' style='stop-color:%23007A3D;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23005c2e;stop-opacity:1' /%3E%3C/linearGradient%3E%3ClinearGradient id='redGrad' x1='0%25' y1='0%25' x2='100%25' y2='0%25'%3E%3Cstop offset='0%25' style='stop-color:%23CE1126;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23a00d1f;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Cpath fill='url(%23greenGrad)' d='M0 0h1200v200H0z'/%3E%3Cpath fill='%23fff' d='M0 200h1200v200H0z'/%3E%3Cpath fill='url(%23redGrad)' d='M0 400h1200v200H0z'/%3E%3Cpath fill='%23000' d='M0 0L400 300L0 600z'/%3E%3C/svg%3E");
}

/* العلم القطري */
.flag-icon-qa {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1100 650'%3E%3Cdefs%3E%3ClinearGradient id='maroonGrad' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%23881034;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%236b0d29;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Cpath fill='%23fff' d='M0 0h1100v650H0z'/%3E%3Cpath fill='url(%23maroonGrad)' d='M0 0h330v650H0z'/%3E%3Cpath fill='url(%23maroonGrad)' d='M330 0l65 32.5-65 32.5 65 32.5-65 32.5 65 32.5-65 32.5 65 32.5-65 32.5 65 32.5-65 32.5 65 32.5-65 32.5 65 32.5-65 32.5 65 32.5-65 32.5 65 32.5-65 32.5 65 32.5-65 32.5v32.5H330z'/%3E%3C/svg%3E");
}

/* العلم البحريني */
.flag-icon-bh {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1000 600'%3E%3Cdefs%3E%3ClinearGradient id='redGrad' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%23CE1126;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23a00d1f;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Cpath fill='url(%23redGrad)' d='M0 0h1000v600H0z'/%3E%3Cpath fill='%23fff' d='M0 0h300v600H0z'/%3E%3Cpath fill='url(%23redGrad)' d='M300 0l60 40-60 40 60 40-60 40 60 40-60 40 60 40-60 40 60 40-60 40 60 40-60 40 60 40-60 40 60 40H300z'/%3E%3C/svg%3E");
}

/* العلم العماني */
.flag-icon-om {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 600'%3E%3Cdefs%3E%3ClinearGradient id='redGrad' x1='0%25' y1='0%25' x2='100%25' y2='0%25'%3E%3Cstop offset='0%25' style='stop-color:%23ED2939;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23c8202e;stop-opacity:1' /%3E%3C/linearGradient%3E%3ClinearGradient id='greenGrad' x1='0%25' y1='0%25' x2='100%25' y2='0%25'%3E%3Cstop offset='0%25' style='stop-color:%23009639;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23007a2f;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Cpath fill='%23fff' d='M0 0h1200v200H0z'/%3E%3Cpath fill='url(%23redGrad)' d='M0 200h1200v200H0z'/%3E%3Cpath fill='url(%23greenGrad)' d='M0 400h1200v200H0z'/%3E%3Cpath fill='url(%23redGrad)' d='M0 0h400v600H0z'/%3E%3Cg fill='%23fff' stroke='%23ffd700' stroke-width='3'%3E%3Ccircle cx='200' cy='150' r='40'/%3E%3Cpath d='M160 150h80M200 110v80'/%3E%3C/g%3E%3C/svg%3E");
}

/* العلم الأردني */
.flag-icon-jo {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 600'%3E%3Cdefs%3E%3ClinearGradient id='blackGrad' x1='0%25' y1='0%25' x2='100%25' y2='0%25'%3E%3Cstop offset='0%25' style='stop-color:%23000000;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23333333;stop-opacity:1' /%3E%3C/linearGradient%3E%3ClinearGradient id='greenGrad' x1='0%25' y1='0%25' x2='100%25' y2='0%25'%3E%3Cstop offset='0%25' style='stop-color:%23007A3D;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23005c2e;stop-opacity:1' /%3E%3C/linearGradient%3E%3ClinearGradient id='redGrad' x1='0%25' y1='0%25' x2='100%25' y2='0%25'%3E%3Cstop offset='0%25' style='stop-color:%23CE1126;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23a00d1f;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Cpath fill='url(%23blackGrad)' d='M0 0h1200v200H0z'/%3E%3Cpath fill='%23fff' d='M0 200h1200v200H0z'/%3E%3Cpath fill='url(%23greenGrad)' d='M0 400h1200v200H0z'/%3E%3Cpath fill='url(%23redGrad)' d='M0 0L400 300L0 600z'/%3E%3Cg fill='%23fff'%3E%3Cpath d='M200 250l15 30h30l-25 20 10 30-30-20-30 20 10-30-25-20h30z'/%3E%3C/g%3E%3C/svg%3E");
}

/* العلم اللبناني */
.flag-icon-lb {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 900 600'%3E%3Cdefs%3E%3ClinearGradient id='redGrad' x1='0%25' y1='0%25' x2='100%25' y2='0%25'%3E%3Cstop offset='0%25' style='stop-color:%23ED2939;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23c8202e;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Cpath fill='url(%23redGrad)' d='M0 0h900v150H0z'/%3E%3Cpath fill='%23fff' d='M0 150h900v300H0z'/%3E%3Cpath fill='url(%23redGrad)' d='M0 450h900v150H0z'/%3E%3Cg fill='%23007A3D' stroke='%23228B22' stroke-width='2'%3E%3Cpath d='M450 200c-30 0-50 20-50 50v100c0 30 20 50 50 50s50-20 50-50V250c0-30-20-50-50-50z'/%3E%3Cpath d='M400 220h100M380 240h140M370 260h160M360 280h180M350 300h200M340 320h220M330 340h240'/%3E%3C/g%3E%3C/svg%3E");
}

/* العلم السوري */
.flag-icon-sy {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 900 600'%3E%3Cdefs%3E%3ClinearGradient id='redGrad' x1='0%25' y1='0%25' x2='100%25' y2='0%25'%3E%3Cstop offset='0%25' style='stop-color:%23CE1126;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23a00d1f;stop-opacity:1' /%3E%3C/linearGradient%3E%3ClinearGradient id='greenGrad' x1='0%25' y1='0%25' x2='100%25' y2='0%25'%3E%3Cstop offset='0%25' style='stop-color:%23007A3D;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23005c2e;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Cpath fill='url(%23redGrad)' d='M0 0h900v200H0z'/%3E%3Cpath fill='%23fff' d='M0 200h900v200H0z'/%3E%3Cpath fill='%23000' d='M0 400h900v200H0z'/%3E%3Cg fill='%23007A3D'%3E%3Cpath d='M300 250l15 30h30l-25 20 10 30-30-20-30 20 10-30-25-20h30z'/%3E%3Cpath d='M600 250l15 30h30l-25 20 10 30-30-20-30 20 10-30-25-20h30z'/%3E%3C/g%3E%3C/svg%3E");
}

/* تحسينات إضافية للأعلام */
.flag-icon-enhanced {
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.flag-icon-enhanced:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

/* تأثيرات ثلاثية الأبعاد للأعلام */
.flag-icon-3d {
    position: relative;
    transform-style: preserve-3d;
    transition: transform 0.3s ease;
}

.flag-icon-3d:hover {
    transform: rotateY(15deg) rotateX(5deg);
}

.flag-icon-3d::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.1) 50%, rgba(255, 255, 255, 0.1) 100%);
    z-index: -1;
    border-radius: inherit;
    transform: translateZ(-1px);
}

/* تأثيرات الموجة للأعلام */
@keyframes flagWave {
    0%, 100% { 
        transform: perspective(400px) rotateY(0deg) rotateX(0deg);
    }
    25% { 
        transform: perspective(400px) rotateY(5deg) rotateX(2deg);
    }
    50% { 
        transform: perspective(400px) rotateY(0deg) rotateX(0deg);
    }
    75% { 
        transform: perspective(400px) rotateY(-5deg) rotateX(-2deg);
    }
}

.flag-icon-wave {
    animation: flagWave 3s ease-in-out infinite;
    transform-origin: left center;
}

.flag-icon-wave:hover {
    animation-duration: 1s;
}

/* تنسيقات خاصة للأعلام في قائمة اللغة */
.language-selector .flag-icon {
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.language-selector .flag-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(0, 0, 0, 0.05) 100%);
    pointer-events: none;
}

/* تحسينات للأداء */
.flag-icon-enhanced,
.flag-icon-3d,
.flag-icon-wave {
    will-change: transform;
    backface-visibility: hidden;
}

/* دعم الشاشات عالية الدقة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .flag-icon {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* تنسيقات للطباعة */
@media print {
    .flag-icon {
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
}
