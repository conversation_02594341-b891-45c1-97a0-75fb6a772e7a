<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض توضيحي لأيقونات الأعلام</title>
    <link rel="stylesheet" href="assets/css/flag-icons.css">
    <link rel="stylesheet" href="assets/css/enhanced-flags.css">
    <link rel="stylesheet" href="assets/css/language-selector.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        :root {
            --color-primary: #4a6cf7;
            --color-primary-rgb: 74, 108, 247;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-section {
            margin: 30px 0;
            padding: 25px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .demo-title {
            color: #333;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 20px;
            text-align: center;
            border-bottom: 3px solid var(--color-primary);
            padding-bottom: 10px;
        }
        
        .flags-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .flag-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .flag-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: var(--color-primary);
        }
        
        .flag-name {
            font-weight: 600;
            color: #333;
        }
        
        .language-demo {
            background: #2a2a2a;
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
        }
        
        .nav-demo {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
        }
        
        .nav-area {
            position: relative;
        }
        
        .nav-area ul {
            list-style: none;
            margin: 0;
            padding: 0;
        }
        
        .main-nav {
            position: relative;
        }
        
        .submenu {
            position: absolute;
            top: 100%;
            right: 0;
            z-index: 1000;
        }
        
        .code-example {
            background: #1e1e1e;
            color: #f8f8f2;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .effects-demo {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 30px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .effect-item {
            text-align: center;
            padding: 20px;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .effect-label {
            margin-top: 10px;
            font-weight: 600;
            color: #666;
            font-size: 14px;
        }
        
        @media (max-width: 768px) {
            .flags-grid {
                grid-template-columns: 1fr;
            }
            
            .effects-demo {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: white; font-size: 36px; margin-bottom: 40px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
            🏳️ عرض توضيحي لأيقونات الأعلام 🏳️
        </h1>
        
        <!-- الأعلام الأساسية -->
        <div class="demo-section">
            <h2 class="demo-title">الأعلام الأساسية</h2>
            <div class="flags-grid">
                <div class="flag-item">
                    <i class="flag-icon flag-icon-eg"></i>
                    <span class="flag-name">مصر (العربية)</span>
                </div>
                <div class="flag-item">
                    <i class="flag-icon flag-icon-gb"></i>
                    <span class="flag-name">المملكة المتحدة (الإنجليزية)</span>
                </div>
                <div class="flag-item">
                    <i class="flag-icon flag-icon-sa"></i>
                    <span class="flag-name">السعودية</span>
                </div>
                <div class="flag-item">
                    <i class="flag-icon flag-icon-ae"></i>
                    <span class="flag-name">الإمارات</span>
                </div>
                <div class="flag-item">
                    <i class="flag-icon flag-icon-kw"></i>
                    <span class="flag-name">الكويت</span>
                </div>
                <div class="flag-item">
                    <i class="flag-icon flag-icon-qa"></i>
                    <span class="flag-name">قطر</span>
                </div>
                <div class="flag-item">
                    <i class="flag-icon flag-icon-bh"></i>
                    <span class="flag-name">البحرين</span>
                </div>
                <div class="flag-item">
                    <i class="flag-icon flag-icon-om"></i>
                    <span class="flag-name">عُمان</span>
                </div>
                <div class="flag-item">
                    <i class="flag-icon flag-icon-jo"></i>
                    <span class="flag-name">الأردن</span>
                </div>
                <div class="flag-item">
                    <i class="flag-icon flag-icon-lb"></i>
                    <span class="flag-name">لبنان</span>
                </div>
                <div class="flag-item">
                    <i class="flag-icon flag-icon-sy"></i>
                    <span class="flag-name">سوريا</span>
                </div>
                <div class="flag-item">
                    <i class="flag-icon flag-icon-us"></i>
                    <span class="flag-name">الولايات المتحدة</span>
                </div>
            </div>
        </div>
        
        <!-- تأثيرات الأعلام -->
        <div class="demo-section">
            <h2 class="demo-title">تأثيرات الأعلام</h2>
            <div class="effects-demo">
                <div class="effect-item">
                    <i class="flag-icon flag-icon-eg flag-icon-enhanced"></i>
                    <div class="effect-label">محسن</div>
                </div>
                <div class="effect-item">
                    <i class="flag-icon flag-icon-gb flag-icon-3d"></i>
                    <div class="effect-label">ثلاثي الأبعاد</div>
                </div>
                <div class="effect-item">
                    <i class="flag-icon flag-icon-sa flag-icon-wave"></i>
                    <div class="effect-label">موجة</div>
                </div>
                <div class="effect-item">
                    <i class="flag-icon flag-icon-ae flag-icon-large"></i>
                    <div class="effect-label">كبير</div>
                </div>
                <div class="effect-item">
                    <i class="flag-icon flag-icon-kw flag-icon-medium"></i>
                    <div class="effect-label">متوسط</div>
                </div>
                <div class="effect-item">
                    <i class="flag-icon flag-icon-qa flag-icon-small"></i>
                    <div class="effect-label">صغير</div>
                </div>
            </div>
        </div>
        
        <!-- عرض قائمة اللغة -->
        <div class="demo-section">
            <h2 class="demo-title">قائمة اختيار اللغة</h2>
            <div class="language-demo">
                <div class="nav-demo">
                    <div class="nav-area language-selector">
                        <ul>
                            <li class="main-nav has-dropdown">
                                <a href="javascript:void(0)" class="language-toggle">
                                    <i class="flag-icon flag-icon-eg"></i>
                                    <span class="language-name">العربية</span>
                                    <i class="fas fa-chevron-down language-arrow"></i>
                                </a>
                                <ul class="submenu parent-nav language-dropdown">
                                    <li class="language-item active">
                                        <a href="#" class="language-link">
                                            <i class="flag-icon flag-icon-eg"></i>
                                            <span class="language-text">العربية</span>
                                            <i class="fas fa-check language-check"></i>
                                        </a>
                                    </li>
                                    <li class="language-item">
                                        <a href="#" class="language-link">
                                            <i class="flag-icon flag-icon-gb"></i>
                                            <span class="language-text">English</span>
                                        </a>
                                    </li>
                                    <li class="language-item">
                                        <a href="#" class="language-link">
                                            <i class="flag-icon flag-icon-fr"></i>
                                            <span class="language-text">Français</span>
                                        </a>
                                    </li>
                                    <li class="language-item">
                                        <a href="#" class="language-link">
                                            <i class="flag-icon flag-icon-de"></i>
                                            <span class="language-text">Deutsch</span>
                                        </a>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- أمثلة الكود -->
        <div class="demo-section">
            <h2 class="demo-title">أمثلة الكود</h2>
            
            <h3>HTML الأساسي:</h3>
            <div class="code-example">
&lt;i class="flag-icon flag-icon-eg"&gt;&lt;/i&gt; العربية
&lt;i class="flag-icon flag-icon-gb"&gt;&lt;/i&gt; English
            </div>
            
            <h3>مع التأثيرات:</h3>
            <div class="code-example">
&lt;i class="flag-icon flag-icon-eg flag-icon-enhanced"&gt;&lt;/i&gt;
&lt;i class="flag-icon flag-icon-gb flag-icon-3d"&gt;&lt;/i&gt;
&lt;i class="flag-icon flag-icon-sa flag-icon-wave"&gt;&lt;/i&gt;
            </div>
            
            <h3>أحجام مختلفة:</h3>
            <div class="code-example">
&lt;i class="flag-icon flag-icon-eg flag-icon-small"&gt;&lt;/i&gt;
&lt;i class="flag-icon flag-icon-eg flag-icon-medium"&gt;&lt;/i&gt;
&lt;i class="flag-icon flag-icon-eg flag-icon-large"&gt;&lt;/i&gt;
            </div>
            
            <h3>في قائمة اللغة:</h3>
            <div class="code-example">
&lt;div class="nav-area language-selector"&gt;
    &lt;ul&gt;
        &lt;li class="main-nav has-dropdown"&gt;
            &lt;a href="#" class="language-toggle"&gt;
                &lt;i class="flag-icon flag-icon-eg"&gt;&lt;/i&gt;
                &lt;span class="language-name"&gt;العربية&lt;/span&gt;
                &lt;i class="fas fa-chevron-down language-arrow"&gt;&lt;/i&gt;
            &lt;/a&gt;
            &lt;ul class="submenu parent-nav language-dropdown"&gt;
                &lt;li class="language-item"&gt;
                    &lt;a href="#" class="language-link"&gt;
                        &lt;i class="flag-icon flag-icon-gb"&gt;&lt;/i&gt;
                        &lt;span class="language-text"&gt;English&lt;/span&gt;
                    &lt;/a&gt;
                &lt;/li&gt;
            &lt;/ul&gt;
        &lt;/li&gt;
    &lt;/ul&gt;
&lt;/div&gt;
            </div>
        </div>
        
        <!-- ملاحظات مهمة -->
        <div class="demo-section" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <h2 class="demo-title" style="color: white; border-color: white;">ملاحظات مهمة</h2>
            <div style="text-align: right; line-height: 1.8; font-size: 16px;">
                <p><strong>✓</strong> جميع الأعلام مصممة بتقنية SVG عالية الجودة</p>
                <p><strong>✓</strong> متوافقة مع جميع المتصفحات الحديثة</p>
                <p><strong>✓</strong> تدعم الشاشات عالية الدقة (Retina)</p>
                <p><strong>✓</strong> محسنة للأداء مع CSS3</p>
                <p><strong>✓</strong> تدعم RTL و LTR</p>
                <p><strong>✓</strong> متجاوبة مع جميع أحجام الشاشات</p>
                <p><strong>✓</strong> تأثيرات حركية سلسة</p>
                <p><strong>✓</strong> سهلة التخصيص والتعديل</p>
            </div>
        </div>
    </div>
    
    <script>
        // إضافة تفاعل للقائمة المنسدلة
        document.addEventListener('DOMContentLoaded', function() {
            const languageToggle = document.querySelector('.language-toggle');
            const languageDropdown = document.querySelector('.language-dropdown');
            
            if (languageToggle && languageDropdown) {
                languageToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    languageDropdown.style.display = languageDropdown.style.display === 'block' ? 'none' : 'block';
                });
                
                // إغلاق القائمة عند النقر خارجها
                document.addEventListener('click', function(e) {
                    if (!e.target.closest('.language-selector')) {
                        languageDropdown.style.display = 'none';
                    }
                });
            }
        });
    </script>
</body>
</html>
