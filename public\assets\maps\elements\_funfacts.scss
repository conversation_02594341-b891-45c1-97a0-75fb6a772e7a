// fun facts area start

.signle-fun-facts-one {
    text-align: center;
    background: #F2F2F2;
    padding: 30px;

    .icon {
        height: 92px;
        width: 92px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: auto;
        border-radius: 50%;
        margin-bottom: 20px;
        background: var(--color-heading-1);
    }

    .title {
        font-size: 48px;
        margin-bottom: 5px;
    }

    span.bototm {
        font-size: 18px;
        color: var(--color-heading-1);
    }
}

.index-one {
    overflow-x: visible;

    @media #{$smlg-device} {
        overflow-x: hidden;
    }
}

.title-area-between-9 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 50px;

    @media #{$mdsm-layout} {
        flex-direction: column;
        align-items: flex-start;
    }

    @media #{$sm-layout} {
        gap: 0;
    }

    >div {
        flex-basis: 112%;
    }
}