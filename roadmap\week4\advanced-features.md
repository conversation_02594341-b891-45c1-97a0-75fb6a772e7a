# الميزات المتقدمة - الأسبوع الرابع

## اليوم 22-24: نظام إدارة المحتوى

### 1. لوحة تحكم للمحتوى
```php
// AdminController.php
class AdminController extends Controller {
    public function dashboard() {
        $stats = [
            'products' => Product::count(),
            'inquiries' => Inquiry::count(),
            'visitors' => $this->getVisitorStats(),
            'orders' => Order::count()
        ];
        
        return view('admin.dashboard', compact('stats'));
    }
    
    public function products() {
        $products = Product::paginate(10);
        return view('admin.products.index', compact('products'));
    }
    
    public function createProduct() {
        return view('admin.products.create');
    }
    
    public function storeProduct(Request $request) {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'category' => 'required|string',
            'specifications' => 'required|array',
            'images' => 'required|array',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);
        
        $product = Product::create($validated);
        
        // Handle image uploads
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $path = $image->store('products', 'public');
                $product->images()->create(['path' => $path]);
            }
        }
        
        return redirect()->route('admin.products.index')
                        ->with('success', 'تم إضافة المنتج بنجاح');
    }
}
```

### 2. نموذج إضافة المنتجات
```html
<!-- admin/products/create.blade.php -->
<div class="admin-container">
    <div class="admin-header">
        <h1>إضافة منتج جديد</h1>
        <a href="{{ route('admin.products.index') }}" class="btn-back">العودة للقائمة</a>
    </div>
    
    <form id="product-form" action="{{ route('admin.products.store') }}" method="POST" enctype="multipart/form-data">
        @csrf
        
        <div class="form-section">
            <h3>معلومات أساسية</h3>
            
            <div class="form-group">
                <label for="name">اسم المنتج</label>
                <input type="text" id="name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="category">الفئة</label>
                <select id="category" name="category" required>
                    <option value="">اختر الفئة</option>
                    <option value="packaging">ورق التغليف</option>
                    <option value="printing">ورق الطباعة</option>
                    <option value="cardboard">الكرتون</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="description">الوصف</label>
                <textarea id="description" name="description" rows="5" required></textarea>
            </div>
        </div>
        
        <div class="form-section">
            <h3>المواصفات التقنية</h3>
            
            <div id="specifications-container">
                <div class="specification-item">
                    <input type="text" name="specifications[0][name]" placeholder="اسم المواصفة">
                    <input type="text" name="specifications[0][value]" placeholder="القيمة">
                    <button type="button" class="remove-spec">حذف</button>
                </div>
            </div>
            
            <button type="button" id="add-specification" class="btn-add">إضافة مواصفة</button>
        </div>
        
        <div class="form-section">
            <h3>الصور</h3>
            
            <div class="image-upload-area">
                <input type="file" id="images" name="images[]" multiple accept="image/*" hidden>
                <div class="upload-zone" onclick="document.getElementById('images').click()">
                    <div class="upload-icon">📷</div>
                    <p>اضغط لرفع الصور أو اسحبها هنا</p>
                    <small>يمكن رفع عدة صور (JPG, PNG, GIF)</small>
                </div>
                
                <div id="image-preview" class="image-preview-container"></div>
            </div>
        </div>
        
        <div class="form-actions">
            <button type="submit" class="btn-primary">حفظ المنتج</button>
            <button type="button" class="btn-secondary" onclick="history.back()">إلغاء</button>
        </div>
    </form>
</div>
```

### 3. JavaScript لإدارة النموذج
```javascript
// Product Form Management
class ProductFormManager {
    constructor() {
        this.specificationCount = 1;
        this.init();
    }
    
    init() {
        this.setupSpecifications();
        this.setupImageUpload();
        this.setupFormValidation();
    }
    
    setupSpecifications() {
        document.getElementById('add-specification').addEventListener('click', () => {
            this.addSpecification();
        });
        
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('remove-spec')) {
                this.removeSpecification(e.target);
            }
        });
    }
    
    addSpecification() {
        const container = document.getElementById('specifications-container');
        const specItem = document.createElement('div');
        specItem.className = 'specification-item';
        specItem.innerHTML = `
            <input type="text" name="specifications[${this.specificationCount}][name]" placeholder="اسم المواصفة">
            <input type="text" name="specifications[${this.specificationCount}][value]" placeholder="القيمة">
            <button type="button" class="remove-spec">حذف</button>
        `;
        
        container.appendChild(specItem);
        this.specificationCount++;
    }
    
    removeSpecification(button) {
        const specItem = button.parentElement;
        specItem.remove();
    }
    
    setupImageUpload() {
        const imageInput = document.getElementById('images');
        const previewContainer = document.getElementById('image-preview');
        
        imageInput.addEventListener('change', (e) => {
            this.previewImages(e.target.files, previewContainer);
        });
        
        // Drag and drop
        const uploadZone = document.querySelector('.upload-zone');
        
        uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadZone.classList.add('drag-over');
        });
        
        uploadZone.addEventListener('dragleave', () => {
            uploadZone.classList.remove('drag-over');
        });
        
        uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('drag-over');
            
            const files = e.dataTransfer.files;
            imageInput.files = files;
            this.previewImages(files, previewContainer);
        });
    }
    
    previewImages(files, container) {
        container.innerHTML = '';
        
        Array.from(files).forEach((file, index) => {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const preview = document.createElement('div');
                    preview.className = 'image-preview-item';
                    preview.innerHTML = `
                        <img src="${e.target.result}" alt="Preview ${index + 1}">
                        <button type="button" class="remove-image" data-index="${index}">×</button>
                    `;
                    container.appendChild(preview);
                };
                reader.readAsDataURL(file);
            }
        });
    }
    
    setupFormValidation() {
        const form = document.getElementById('product-form');
        
        form.addEventListener('submit', (e) => {
            if (!this.validateForm()) {
                e.preventDefault();
            }
        });
    }
    
    validateForm() {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                this.showFieldError(field, 'هذا الحقل مطلوب');
                isValid = false;
            }
        });
        
        return isValid;
    }
    
    showFieldError(field, message) {
        field.classList.add('error');
        
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error';
        errorElement.textContent = message;
        
        field.parentNode.appendChild(errorElement);
        
        setTimeout(() => {
            field.classList.remove('error');
            if (errorElement.parentNode) {
                errorElement.parentNode.removeChild(errorElement);
            }
        }, 3000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ProductFormManager();
});
```

## اليوم 25-26: نظام التحليلات

### 1. تتبع الزوار والتفاعل
```javascript
// Analytics System
class AnalyticsManager {
    constructor() {
        this.sessionId = this.generateSessionId();
        this.startTime = Date.now();
        this.events = [];
        this.init();
    }
    
    init() {
        this.trackPageView();
        this.setupEventTracking();
        this.setupScrollTracking();
        this.setupTimeTracking();
    }
    
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    trackPageView() {
        this.sendEvent('page_view', {
            url: window.location.href,
            title: document.title,
            referrer: document.referrer,
            timestamp: Date.now()
        });
    }
    
    setupEventTracking() {
        // Track button clicks
        document.addEventListener('click', (e) => {
            if (e.target.matches('button, .btn, a')) {
                this.trackEvent('click', {
                    element: e.target.tagName,
                    text: e.target.textContent.trim(),
                    href: e.target.href || null,
                    position: this.getElementPosition(e.target)
                });
            }
        });
        
        // Track form submissions
        document.addEventListener('submit', (e) => {
            this.trackEvent('form_submit', {
                form: e.target.id || 'unnamed_form',
                action: e.target.action
            });
        });
        
        // Track product views
        document.addEventListener('click', (e) => {
            if (e.target.closest('.product-card')) {
                const productCard = e.target.closest('.product-card');
                this.trackEvent('product_view', {
                    product_id: productCard.dataset.productId,
                    product_name: productCard.querySelector('.product-title')?.textContent
                });
            }
        });
    }
    
    setupScrollTracking() {
        let maxScroll = 0;
        const milestones = [25, 50, 75, 100];
        const reached = new Set();
        
        window.addEventListener('scroll', () => {
            const scrollPercent = Math.round(
                (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100
            );
            
            maxScroll = Math.max(maxScroll, scrollPercent);
            
            milestones.forEach(milestone => {
                if (scrollPercent >= milestone && !reached.has(milestone)) {
                    reached.add(milestone);
                    this.trackEvent('scroll_milestone', {
                        percentage: milestone
                    });
                }
            });
        });
    }
    
    setupTimeTracking() {
        // Track time spent on page
        window.addEventListener('beforeunload', () => {
            const timeSpent = Date.now() - this.startTime;
            this.trackEvent('time_on_page', {
                duration: timeSpent,
                max_scroll: maxScroll
            });
        });
        
        // Track engagement milestones
        const engagementMilestones = [30, 60, 120, 300]; // seconds
        
        engagementMilestones.forEach(seconds => {
            setTimeout(() => {
                this.trackEvent('engagement_milestone', {
                    seconds: seconds
                });
            }, seconds * 1000);
        });
    }
    
    trackEvent(eventType, data) {
        const event = {
            type: eventType,
            data: data,
            timestamp: Date.now(),
            session_id: this.sessionId,
            url: window.location.href
        };
        
        this.events.push(event);
        this.sendEvent(eventType, data);
    }
    
    sendEvent(eventType, data) {
        // Send to server
        fetch('/analytics/track', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                event_type: eventType,
                data: data,
                session_id: this.sessionId
            })
        }).catch(error => {
            console.error('Analytics error:', error);
        });
    }
    
    getElementPosition(element) {
        const rect = element.getBoundingClientRect();
        return {
            x: rect.left + window.scrollX,
            y: rect.top + window.scrollY
        };
    }
}

// Initialize analytics
document.addEventListener('DOMContentLoaded', () => {
    new AnalyticsManager();
});
```

### 2. لوحة تحكم التحليلات
```php
// AnalyticsController.php
class AnalyticsController extends Controller {
    public function track(Request $request) {
        $validated = $request->validate([
            'event_type' => 'required|string',
            'data' => 'required|array',
            'session_id' => 'required|string'
        ]);
        
        AnalyticsEvent::create([
            'event_type' => $validated['event_type'],
            'data' => json_encode($validated['data']),
            'session_id' => $validated['session_id'],
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'created_at' => now()
        ]);
        
        return response()->json(['status' => 'success']);
    }
    
    public function dashboard() {
        $stats = [
            'total_visitors' => $this->getTotalVisitors(),
            'page_views' => $this->getPageViews(),
            'popular_products' => $this->getPopularProducts(),
            'conversion_rate' => $this->getConversionRate()
        ];
        
        return view('admin.analytics.dashboard', compact('stats'));
    }
    
    private function getTotalVisitors() {
        return AnalyticsEvent::where('event_type', 'page_view')
                           ->distinct('session_id')
                           ->count();
    }
    
    private function getPageViews() {
        return AnalyticsEvent::where('event_type', 'page_view')->count();
    }
    
    private function getPopularProducts() {
        return AnalyticsEvent::where('event_type', 'product_view')
                           ->selectRaw('JSON_EXTRACT(data, "$.product_name") as product_name, COUNT(*) as views')
                           ->groupBy('product_name')
                           ->orderBy('views', 'desc')
                           ->limit(10)
                           ->get();
    }
}
```

## اليوم 27-28: PWA والميزات المتقدمة

### 1. Progressive Web App
```json
// manifest.json
{
    "name": "شركة الورق المتقدمة",
    "short_name": "ورق متقدم",
    "description": "رائدون في صناعة الورق عالي الجودة",
    "start_url": "/",
    "display": "standalone",
    "background_color": "#ffffff",
    "theme_color": "#007bff",
    "orientation": "portrait",
    "icons": [
        {
            "src": "/images/icons/icon-72x72.png",
            "sizes": "72x72",
            "type": "image/png"
        },
        {
            "src": "/images/icons/icon-96x96.png",
            "sizes": "96x96",
            "type": "image/png"
        },
        {
            "src": "/images/icons/icon-128x128.png",
            "sizes": "128x128",
            "type": "image/png"
        },
        {
            "src": "/images/icons/icon-144x144.png",
            "sizes": "144x144",
            "type": "image/png"
        },
        {
            "src": "/images/icons/icon-152x152.png",
            "sizes": "152x152",
            "type": "image/png"
        },
        {
            "src": "/images/icons/icon-192x192.png",
            "sizes": "192x192",
            "type": "image/png"
        },
        {
            "src": "/images/icons/icon-384x384.png",
            "sizes": "384x384",
            "type": "image/png"
        },
        {
            "src": "/images/icons/icon-512x512.png",
            "sizes": "512x512",
            "type": "image/png"
        }
    ]
}
```

### 2. Push Notifications
```javascript
// Push Notification Manager
class PushNotificationManager {
    constructor() {
        this.init();
    }
    
    async init() {
        if ('serviceWorker' in navigator && 'PushManager' in window) {
            await this.registerServiceWorker();
            await this.requestPermission();
        }
    }
    
    async registerServiceWorker() {
        try {
            const registration = await navigator.serviceWorker.register('/service-worker.js');
            console.log('Service Worker registered:', registration);
            return registration;
        } catch (error) {
            console.error('Service Worker registration failed:', error);
        }
    }
    
    async requestPermission() {
        const permission = await Notification.requestPermission();
        
        if (permission === 'granted') {
            console.log('Notification permission granted');
            await this.subscribeUser();
        } else {
            console.log('Notification permission denied');
        }
    }
    
    async subscribeUser() {
        try {
            const registration = await navigator.serviceWorker.ready;
            const subscription = await registration.pushManager.subscribe({
                userVisibleOnly: true,
                applicationServerKey: this.urlBase64ToUint8Array(
                    'YOUR_VAPID_PUBLIC_KEY' // Replace with your VAPID public key
                )
            });
            
            // Send subscription to server
            await this.sendSubscriptionToServer(subscription);
        } catch (error) {
            console.error('Failed to subscribe user:', error);
        }
    }
    
    async sendSubscriptionToServer(subscription) {
        await fetch('/push-notifications/subscribe', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify(subscription)
        });
    }
    
    urlBase64ToUint8Array(base64String) {
        const padding = '='.repeat((4 - base64String.length % 4) % 4);
        const base64 = (base64String + padding)
            .replace(/-/g, '+')
            .replace(/_/g, '/');
        
        const rawData = window.atob(base64);
        const outputArray = new Uint8Array(rawData.length);
        
        for (let i = 0; i < rawData.length; ++i) {
            outputArray[i] = rawData.charCodeAt(i);
        }
        return outputArray;
    }
}

// Initialize push notifications
document.addEventListener('DOMContentLoaded', () => {
    new PushNotificationManager();
});
```

## الملفات المطلوبة للأسبوع الرابع:

```
app/Http/Controllers/
├── AdminController.php
├── AnalyticsController.php
└── PushNotificationController.php

app/Models/
├── AnalyticsEvent.php
└── PushSubscription.php

resources/views/admin/
├── dashboard.blade.php
├── products/
│   ├── index.blade.php
│   ├── create.blade.php
│   └── edit.blade.php
└── analytics/
    └── dashboard.blade.php

public/assets/js/
├── admin-panel.js
├── analytics.js
├── push-notifications.js
└── pwa-installer.js

public/
├── manifest.json
└── images/icons/
    ├── icon-72x72.png
    ├── icon-96x96.png
    └── ... (other icon sizes)
```
