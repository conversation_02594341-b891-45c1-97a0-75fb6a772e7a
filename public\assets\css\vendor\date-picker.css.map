{"version": 3, "sources": ["utils/_variables.scss", "vendors/date-picker/_date-picker.scss"], "names": [], "mappings": "AAcA,MACI,sBAAc,CACd,sBAAiB,CCgCrB,0EACE,aAnCyB,CAqCzB,sFACE,aAA4D,CAC7D,gHAIG,aA3CqB,CA4CtB,gGAID,UDzCW,CC0CX,kBAAyD,CAF1D,gHAIG,kBAAyD,CAC1D,gGAID,qCA5CyB,CA6CzB,UAA2D,CAF5D,gHAKG,qCAhDuB,CAiDxB,sFAKD,eAAgB,CAChB,WAAY,CACb,mBAQD,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,kBAAW,CAAX,cAAe,CAChB,kBAGC,iBAAkB,CAClB,6BAAsB,CAAtB,qBAAsB,CACtB,cAAe,CACf,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,iBAAkB,CAClB,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,WArG0B,CAsG1B,SAAU,CATZ,0BAYI,kBA/EkB,CAmEtB,4BAgBI,aA7FwB,CA8FxB,oBA9FwB,CA6E5B,oCAoBM,aAhGiB,CA4EvB,uCAwBM,aArGsB,CA6E5B,6BA6BI,+BAjGyB,CAkGzB,aA1GmB,CA2GnB,eAAgB,CA/BpB,qCAkCM,qCAtGuB,CAoE7B,6BAuCI,cAAe,CACf,aAtHqB,CA8EzB,qCA2CM,aAzHmB,CA8EzB,wCA+CM,aAAyD,CA/C/D,+CAoDQ,aAlIiB,CA8EzB,+BA0DI,qCAhIqB,CAiIrB,qCA/HyB,CAgIzB,yBAtJwB,CA0F5B,6BA+DI,qCArIqB,CAsIrB,qCApIyB,CAqIzB,yBAAmE,CAjEvE,0CAqEI,iBA/JwB,CA0F5B,6BA0EI,UDnJW,CCoJX,WAAY,CACZ,kBAlJqB,CAsEzB,uCA+EM,UDxJS,CCyJT,kBAtJmB,CAsEzB,qCAoFM,kBAzJmC,CAqEzC,wBAyFI,cAAe,CAChB,wBAOD,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,kBAAe,CAAf,cAAe,CACf,gBAAiB,CAClB,sBAGC,aAlLuB,CAmLvB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,kBAAO,CAAP,UAAO,CAAP,MAAO,CACP,iBAAkB,CAClB,wBAAyB,CACzB,cAAe,CAChB,sBAMC,eAAO,CACP,WAAY,CAIb,0BASC,YA9N6B,CA+N9B,wBAMC,YAAa,CACb,UAAW,CACZ,mBAMC,YA7O6B,CA8O9B,yBAGC,YAjP6B,CAkP9B,uBAKC,SAAoC,CACpC,aAAc,CAKf,uBAOC,iBAAkB,CAClB,MAAO,CACP,KAAM,CAEN,aALF,uBAMI,YAAa,CAEhB,CAED,YACE,eD5Pa,CC8Pb,iBA/Q0B,CAgR1B,8BAAuB,CAAvB,sBAAuB,CACvB,gCD3QqB,CC4QrB,cA/QuB,CAgRvB,aArQqB,CAsQrB,WAtRqB,CAuRrB,iBAAkB,CAClB,cAAe,CACf,SAAU,CACV,YAAa,CACb,0EAlP6B,CAkP7B,kEAlP6B,CAkP7B,0DAlP6B,CAkP7B,qFAlP6B,CAmP7B,WAxRoB,CAyRpB,mDAAuB,CAAvB,2CAA2C,CAf7C,uBAiBI,kCAAW,CAAX,0BAAmD,CAjBvD,yBAoBI,iCAAW,CAAX,yBAAkD,CApBtD,0BAuBI,iCAAW,CAAX,yBAAkD,CAvBtD,wBA0BI,kCAAW,CAAX,0BAAmD,CA1BvD,mBA+BI,SAAU,CACV,8BAAuB,CAAvB,sBAAuB,CACvB,yEAAkJ,CAAlJ,iEAAkJ,CAAlJ,yDAAkJ,CAAlJ,oFAAoJ,CACrJ,+BAOC,eAAgB,CAChB,SAAU,CACV,UAAW,CACX,SAAU,CACV,sBAAW,CAAX,cAAe,CARnB,wCAYI,YAAa,CACd,qBAID,8BAAuB,CAAvB,sBAAuB,CACvB,WAlUqB,CAoUrB,wCACE,YAAa,CACd,qBAQD,iBAAkB,CAClB,eD/Ta,CCgUb,4BApTsB,CAqTtB,8BArTsB,CAsTtB,UAhT0B,CAiT1B,WAjT0B,CAkT1B,UAAW,CAKX,sGACE,oBAAqC,CACrC,gCAAW,CAAX,wBAAyB,CAC1B,4GAGC,sBAAuC,CACvC,gCAAW,CAAX,wBAAyB,CAC1B,+GAGC,uBAAwC,CACxC,gCAAW,CAAX,wBAAyB,CAC1B,yGAGC,qBAAsC,CACtC,+BAAW,CAAX,uBAAwB,CACzB,qEAMC,SA9U0B,CA+U3B,uEAEC,UAjV0B,CAkV3B,yEAEC,yBAA+C,CAChD,mEAGC,QAxV0B,CAyV3B,yEAEC,WA3V0B,CA4V3B,yEAEC,wBAA8C,CAC/C,kBAQD,YAAa,CADf,yBAII,wBAAyB,CAC1B,iBAQD,aD3TqB,CC4TrB,wBAAyB,CACzB,kBAAmB,CACnB,eAAgB,CAChB,iBAAkB,CAClB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,wBAA8B,CAA9B,qBAA8B,CAA9B,6BAA8B,CAC9B,+BAnYkB,CAoYlB,eA/X+B,CAgY/B,WA/ZqB,CAiarB,oCACE,YAAa,CACd,+CAKD,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,cAAe,CACf,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,uBAAiB,CAAjB,oBAAiB,CAAjB,sBAAuB,CACxB,wBAGC,UAnb0B,CAob1B,iBAAkB,CAClB,wBAAiB,CAAjB,qBAAiB,CAAjB,oBAAiB,CAAjB,gBAAiB,CACjB,kBDvVqB,CCmVvB,8BAOI,kBD1VmB,CCmVvB,mCAWI,iBAAkB,CAXtB,4BAeI,UAAW,CACX,WAAY,CAhBhB,6BAoBI,SAAU,CACV,WDnbW,CCobX,gBAAiB,CAClB,uBAID,iBA1c0B,CA2c1B,aAAc,CAFhB,yBAKI,iBAAkB,CAClB,aDnXmB,CCoXnB,eAAgB,CAPpB,kCAeI,cAAe,CACf,eAAgB,CACjB,qBAOD,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,WAjeqB,CAkerB,4BAxckB,CAycnB,oBAGC,aA1d0B,CA2d1B,cAAe,CACf,iBAze0B,CA0e1B,kBAAO,CAAP,UAAO,CAAP,MAAO,CACP,0BAAoB,CAApB,0BAAoB,CAApB,mBAAoB,CACpB,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,WAAY,CARd,0BAWI,aAnemB,CAoenB,kBA3dkB", "file": "vendors/date-picker.css", "sourcesContent": ["//fonts\n$font_0: Lato;\n$font_1: sans-serif;\n$font_2: Philosopher;\n$font_3: Cormorant;\n$font-themify: themify;\n$font-awesome: FontAwesome;\n$font-ICO: IcoFont;\n$font-work-sans: work-Sans;\n$font-nunito: <PERSON><PERSON><PERSON>;\n$font-serif: sans-serif;\n$montserrat: \"Montserrat\",\nsans-serif;\n\n:root {\n    --theme-color: #ec8951;\n    --bs-table-color: #222;\n}\n\n//colors\n$gray-90: #e5e5e5;\n$white: #ffffff;\n$white-dark: #eeeeee;\n$black: #000000;\n$grey01: #111111;\n$dark-font: #222222;\n$grey-dark: #2d2a25;\n$font-color: #333333;\n$grey: #777777;\n$grey-light: #a1a1a1;\n$border: #dddada;\n$round-border: #dddddd;\n$dark-grey: #565656;\n$dark-silver: #909090;\n$grey-lighter: #f9f9f9;\n$theme-default: #ec8951;\n$grey-darken: #393230;\n$grey-link: #948e8c;\n$grey-font: #938d8c;\n$dark-footer: #2e2726;\n$form-bg: #f5f2f2;\n$grey-shade: #7f786d;\n$darker-footer: #25221e;\n$border-color: #38352f;\n$border-grey: #f1f5f4;\n$font-grey: #aaaaaa;\n$star-yellow: #ffa200;\n$border-bottom-g: #525252;\n$top-header: #f8f8f8;\n$header-font: #999999;\n$shadow: #ededed;\n$box-border: #ecececa8;\n$grey-about: #f7f7f7;\n$grey2: #555555;\n$grey3: #efefef;\n$silver-light-shade: #ced4da;\n$grey4: #444444;\n$grey5: #888888;\n$grey6: #6f6f6f;\n$grey7: #40494f;\n$grey8: #c0c0c0;\n$sidebar-border: #f5f2f2;\n$sidebar-color: #938d8c;\n$modal: #ffba00;\n$bg-color: #d0edff;\n$bg-color1: #f1e7e6;\n$bg-color2: #bfbfbf;\n$light-grey: #d0edff;\n$pink: #f1e7e6;\n$blue: #bfbfbf;\n$icon: #6f6f6f;\n$color-red: #ff4c3b;\n$gradient1: #01effc;\n$gradient2: #485ff2;\n$gray: #bbbbbb;\n$left-sidebar: #2b2b2b;\n$skin-color: #f8efe7;\n\n// dark layout variable //\n$dark-body: #2b2b2b;\n$dark-top: #232323;\n$dark-border: #404040;\n$dark-link: #cbcbcb;\n$dark-span: #929292;\n$dark-footer-bg: #383838;\n// $white-dark: #40404040;\n$white-1: #f1f3f5;\n$white-2: #eaedef;\n$white-3: #e7eaec;\n$white-4: #dee2e6;\n$white-5: #cfd4da;\n\n// skeleton loader colors\n$bg-loader: #f3f3f3;\n\n// Theme colors variables\n$primary-color: #ff8084;\n$secondary-color: #13c9ca;\n$success-color: #81ba00;\n$info-color: #00a8ff;\n$warning-color: #ffbc58;\n$danger-color: #a5a5a5;\n$light-color: #f8f8f9;\n$semi-dark: #aaaaaa;\n$light-semi-gray: #eff0f1;\n$light-gray: #e8ebf2;\n$dark-gray: #898989;\n$dark-color: #2a3142;\n$gray-60: #999999;\n$transparent-color: transparent;\n$auth-bg-color: #fafafa;\n$light: #f6f6f6;\n$light-text: #999;", "@import \"../../utils/variables\";\n$datepickerDayCellSize: 32px !default;\n$datepickerWidth: 250px !default;\n$datepickerMinBodyHeight: 170px !default;\n$datepickerBorderRadius: 8px !default;\n$datepickerPadding: 4px !default;\n$datepickerZIndex: 100 !default;\n$datepickerFontSize: 14px !default;\n$datepickerYearsPerRow: 4 !default;\n//$info-color   :#ab8ce4;\n$white :$white;\n\n$datepickerTextColor: (\n        button: #5cc4ef,\n        otherMonth: #dedede,\n        otherMonthInRange: #ccc,\n        disabled: #aeaeae,\n        currentDate: #4EB5E6,\n        common: #4a4a4a,\n        dayNames: #FF9A19,\n        navArrows: #9c9c9c\n) !default;\n\n$datepickerBG: (\n        selected: #5cc4ef,\n        selectedHover: darken(#5cc4ef, 5),\n        inRange: rgba(#5cc4ef, .1),\n        hover: #f0f0f0\n) !default;\n\n$datepickerBorderColor: (\n        nav: #efefef,\n        inline: #d7d7d7,\n        default: #dbdbdb\n) !default;\n\n$datepickerNavigationHeight: 32px !default;\n$datepickerNavigationButtonsOffset: 2px !default;\n\n$datepickerPointerSize: 10px !default;\n$datepickerPointerOffset: 10px !default;\n\n// Transitions\n$datepickerTransitionSpeed: .3s !default;\n$datepickerTransitionEase: ease !default;\n$datepickerTransitionOffset: 8px !default;\n\n// Objects\n%otherMonth {\n  color: map_get($datepickerTextColor, otherMonth);\n\n  &:hover {\n    color: darken(map_get($datepickerTextColor, otherMonth), 10);\n  }\n\n  &.-disabled- {\n    &.-focus- {\n      color: map_get($datepickerTextColor, otherMonth);\n    }\n  }\n\n  &.-selected- {\n    color: $white;\n    background: lighten(map_get($datepickerBG, selected), 15);\n    &.-focus- {\n      background: lighten(map_get($datepickerBG, selected), 10);\n    }\n  }\n\n  &.-in-range- {\n    background-color: map_get($datepickerBG, inRange);\n    color: darken(map_get($datepickerTextColor, otherMonth), 7);\n\n    &.-focus- {\n      background-color: rgba(map_get($datepickerBG, inRange), .2);\n    }\n  }\n\n\n  &:empty {\n    background: none;\n    border: none;\n  }\n}\n\n/* -------------------------------------------------\n    Datepicker cells\n   ------------------------------------------------- */\n\n.datepicker--cells {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.datepicker--cell {\n  border-radius: 5px;\n  box-sizing: border-box;\n  cursor: pointer;\n  display: flex;\n  position: relative;\n  align-items: center;\n  justify-content: center;\n  height: $datepickerDayCellSize;\n  z-index: 1;\n\n  &.-focus- {\n    background: map_get($datepickerBG, hover);\n  }\n\n  &.-current- {\n    color: map_get($datepickerTextColor, currentDate);\n    border-color: map_get($datepickerTextColor, currentDate);\n\n    &.-focus- {\n      color: map_get($datepickerTextColor, common);\n    }\n\n    &.-in-range- {\n      color: map_get($datepickerTextColor, currentDate);\n    }\n  }\n\n  &.-in-range- {\n    background: map_get($datepickerBG, inRange);\n    color: map_get($datepickerTextColor, common);\n    border-radius: 0;\n\n    &.-focus- {\n      background-color: rgba(map_get($datepickerBG, inRange), .2);\n    }\n  }\n\n  &.-disabled- {\n    cursor: default;\n    color: map_get($datepickerTextColor, disabled);\n\n    &.-focus- {\n      color: map_get($datepickerTextColor, disabled);\n    }\n\n    &.-in-range- {\n      color: darken(map_get($datepickerTextColor, disabled), 5);\n    }\n\n    &.-current- {\n      &.-focus- {\n        color: map_get($datepickerTextColor, disabled);\n      }\n    }\n  }\n\n  &.-range-from- {\n    border: 1px solid rgba(map_get($datepickerBG, selected), .5);\n    background-color: map_get($datepickerBG, inRange);\n    border-radius: $datepickerBorderRadius 0 0 $datepickerBorderRadius;\n  }\n  &.-range-to- {\n    border: 1px solid rgba(map_get($datepickerBG, selected), .5);\n    background-color: map_get($datepickerBG, inRange);\n    border-radius:  0 $datepickerBorderRadius $datepickerBorderRadius 0;\n  }\n\n  &.-range-from-.-range-to- {\n    border-radius: $datepickerBorderRadius;\n\n  }\n\n  &.-selected- {\n    color: $white;\n    border: none;\n    background: map_get($datepickerBG, selected);\n\n    &.-current- {\n      color: $white;\n      background: map_get($datepickerBG, selected);\n    }\n\n    &.-focus- {\n      background: map_get($datepickerBG, selectedHover);\n    }\n  }\n\n  &:empty {\n    cursor: default;\n  }\n}\n\n//  Day names\n// -------------------------------------------------\n\n.datepicker--days-names {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 8px 0 3px;\n}\n\n.datepicker--day-name {\n  color: map_get($datepickerTextColor, dayNames);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex: 1;\n  text-align: center;\n  text-transform: uppercase;\n  font-size: .8em;\n}\n\n//  Day cell\n// -------------------------------------------------\n\n.datepicker--cell-day {\n  width: (100/7)#{'%'};\n  height: 34px;\n  &.-other-month- {\n    @extend %otherMonth;\n  }\n}\n\n\n//  Months\n// -------------------------------------------------\n\n.datepicker--months {}\n\n.datepicker--cells-months {\n  height: $datepickerMinBodyHeight;\n}\n\n//  Month cell\n// -------------------------\n\n.datepicker--cell-month {\n  width: 33.33%;\n  height: 25%;\n}\n\n//  Years\n// -------------------------------------------------\n\n.datepicker--years {\n  height: $datepickerMinBodyHeight;\n}\n\n.datepicker--cells-years {\n  height: $datepickerMinBodyHeight;\n}\n//  Year cell\n// -------------------------\n\n.datepicker--cell-year {\n  width: 100% / $datepickerYearsPerRow;\n  height: 33.33%;\n\n  &.-other-decade- {\n    @extend %otherMonth;\n  }\n}\n\n/* -------------------------------------------------\n    Datepicker\n   ------------------------------------------------- */\n\n.datepickers-container {\n  position: absolute;\n  left: 0;\n  top: 0;\n\n  @media print {\n    display: none;\n  }\n}\n\n.datepicker {\n  background: $white;\n  // border: 1px solid map_get($datepickerBorderColor, default);\n  border-radius: $datepickerBorderRadius;\n  box-sizing: content-box;\n  font-family: $font-work-sans, $font-serif;\n  font-size: $datepickerFontSize;\n  color: map_get($datepickerTextColor, common);\n  width: $datepickerWidth;\n  position: absolute;\n  left: -100000px;\n  opacity: 0;\n  padding: 10px;\n  transition: opacity $datepickerTransitionSpeed $datepickerTransitionEase, transform $datepickerTransitionSpeed $datepickerTransitionEase, left 0s $datepickerTransitionSpeed;\n  z-index: $datepickerZIndex;\n  box-shadow: 0 4px 14px rgba(174,197,231,.5);\n  &.-from-top- {\n    transform: translateY(-$datepickerTransitionOffset);\n  }\n  &.-from-right- {\n    transform: translateX($datepickerTransitionOffset);\n  }\n  &.-from-bottom- {\n    transform: translateY($datepickerTransitionOffset);\n  }\n  &.-from-left- {\n    transform: translateX(-$datepickerTransitionOffset);\n  }\n\n\n  &.active {\n    opacity: 1;\n    transform: translate(0);\n    transition: opacity $datepickerTransitionSpeed $datepickerTransitionEase, transform $datepickerTransitionSpeed $datepickerTransitionEase, left 0s 0s;\n  }\n}\n\n.datepicker-inline {\n  .datepicker {\n    // border-color: map-get($datepickerBorderColor, inline);\n    // box-shadow: none;\n    position: static;\n    left: auto;\n    right: auto;\n    opacity: 1;\n    transform: none;\n  }\n\n  .datepicker--pointer {\n    display: none;\n  }\n}\n\n.datepicker--content {\n  box-sizing: content-box;\n  padding: $datepickerPadding;\n\n  .-only-timepicker- & {\n    display: none;\n  }\n}\n\n//  Pointer\n// -------------------------------------------------\n$pointerHalfSize: $datepickerPointerSize / 2 - 1;\n\n.datepicker--pointer {\n  position: absolute;\n  background: $white;\n  border-top: 1px solid map-get($datepickerBorderColor, default);\n  border-right: 1px solid map-get($datepickerBorderColor, default);\n  width: $datepickerPointerSize;\n  height: $datepickerPointerSize;\n  z-index: -1;\n\n  //  Main axis\n  // -------------------------\n\n  .-top-left- &, .-top-center- &, .-top-right- & {\n    top: calc(100% - #{$pointerHalfSize});\n    transform: rotate(135deg);\n  }\n\n  .-right-top- &, .-right-center- &, .-right-bottom- & {\n    right: calc(100% - #{$pointerHalfSize});\n    transform: rotate(225deg);\n  }\n\n  .-bottom-left- &, .-bottom-center- &, .-bottom-right- & {\n    bottom: calc(100% - #{$pointerHalfSize});\n    transform: rotate(315deg);\n  }\n\n  .-left-top- &, .-left-center- &, .-left-bottom- & {\n    left: calc(100% - #{$pointerHalfSize});\n    transform: rotate(45deg);\n  }\n\n  //  Secondary axis\n  // -------------------------\n\n  .-top-left- &, .-bottom-left- & {\n    left: $datepickerPointerOffset;\n  }\n  .-top-right- &, .-bottom-right- & {\n    right: $datepickerPointerOffset;\n  }\n  .-top-center- &, .-bottom-center- & {\n    left: calc(50% - #{$datepickerPointerSize} / 2);\n  }\n\n  .-left-top- &, .-right-top- & {\n    top: $datepickerPointerOffset;\n  }\n  .-left-bottom- &, .-right-bottom- & {\n    bottom: $datepickerPointerOffset;\n  }\n  .-left-center- &, .-right-center- & {\n    top: calc(50% - #{$datepickerPointerSize} / 2);\n  }\n\n}\n\n//  Body\n// -------------------------------------------------\n\n.datepicker--body {\n  display: none;\n\n  &.active {\n    display: block !important;\n  }\n}\n\n/* -------------------------------------------------\n    Navigation\n   ------------------------------------------------- */\n\n.datepicker--nav {\n  color: $primary-color;\n  text-transform: uppercase;\n  letter-spacing: 2px;\n  font-weight: 600;\n  border-radius: 5px;\n  display: flex;\n  justify-content: space-between;\n  border-bottom: 1px solid map_get($datepickerBorderColor, nav);\n  min-height: $datepickerNavigationHeight;\n  padding: $datepickerPadding;\n\n  .-only-timepicker- & {\n    display: none;\n  }\n}\n\n.datepicker--nav-title,\n.datepicker--nav-action {\n  display: flex;\n  cursor: pointer;\n  align-items: center;\n  justify-content: center;\n}\n\n.datepicker--nav-action {\n  width: $datepickerDayCellSize;\n  border-radius: 5px;\n  user-select: none;\n  background: $primary-color;\n\n  &:hover {\n    background: $primary-color;\n  }\n\n  &.-disabled- {\n    visibility: hidden;\n  }\n\n  svg {\n    width: 32px;\n    height: 32px;\n  }\n\n  path {\n    fill: none;\n    stroke: $white;\n    stroke-width: 2px;\n  }\n}\n\n.datepicker--nav-title {\n  border-radius: $datepickerBorderRadius;\n  padding: 0 8px;\n\n  i {\n    font-style: normal;\n    color: $primary-color;\n    margin-left: 5px;\n  }\n\n  &:hover {\n\n  }\n\n  &.-disabled- {\n    cursor: default;\n    background: none;\n  }\n}\n\n//  Buttons\n// -------------------------------------------------\n\n.datepicker--buttons {\n  display: flex;\n  padding: $datepickerPadding;\n  border-top: 1px solid map_get($datepickerBorderColor, nav);\n}\n\n.datepicker--button {\n  color: map_get($datepickerTextColor, currentDate);\n  cursor: pointer;\n  border-radius: $datepickerBorderRadius;\n  flex: 1;\n  display: inline-flex;\n  justify-content: center;\n  align-items: center;\n  height: 32px;\n\n  &:hover {\n    color: map_get($datepickerTextColor, common);\n    background: map_get($datepickerBG, hover);\n  }\n}\n"]}