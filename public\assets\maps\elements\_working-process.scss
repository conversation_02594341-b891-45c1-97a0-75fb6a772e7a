.working-process-bg {
    background-image: url(../images/wokring-process/bg-01.jpg);
    max-width: 1680px;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    clip-path: polygon(6.399% 10.697%, 11.726% 0%, 100% 0%, 100% 50%, 100% 81.841%, 95.268% 90.112%, 89.702% 100%, 0% 100%, 0% 22.948%, 6.399% 10.697%);

    @media #{$sm-layout} {
        clip-path: none;
    }
}

.working-process-area-three {
    .title-style-three .bg-title::after {
        position: absolute;
        content: "";
        left: 0;
        bottom: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(0deg, rgb(246, 246, 246) 0%, rgb(246 246 246 / 73%) 35%, rgba(255, 255, 255, 0) 100%);
    }

    .title-style-three {
        z-index: 1;

        .bg-title {
            z-index: -1;
        }
    }
}

.working-process-one.bg-main.rts-section-gap {
    @media #{$smlg-device} {
        overflow: hidden;
    }
}

.rts-working-process-1 {
    display: flex;
    flex-direction: column;
    align-items: center;

    .inner {
        width: 192px;
        height: 192px;
        border: 2px dashed rgba(32, 40, 45, 0.18);
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        margin-bottom: 33px;
        transition: .3s;
        position: relative;

        &.two {
            &::after {
                content: '02';
            }
        }

        &.three {
            &::after {
                content: '03';
            }
        }

        &.four {
            &::after {
                content: '04';
            }
        }

        &::after {
            position: absolute;
            right: 5px;
            top: 7px;
            content: '01';
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: var(--color-primary);
            color: #fff;
            transform: scale(0);
            transition: .3s;

        }

        .icon {
            height: 144px;
            width: 144px;
            background: #fff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    .content {
        .title {
            margin-bottom: 7px;
        }
    }

    &.process-lg {
        .inner {
            width: 245px;
            height: 245px;

            &::after {
                width: 60px;
                height: 60px;

            }

            .icon {
                width: 193.03px;
                height: 193.03px;
            }
        }
    }

    &:hover {
        .inner {
            border: 2px dashed var(--color-primary);

            &::after {
                transform: scale(1);
            }
        }
    }
}

.thumbnail-area-wrapper-inner-6 {
    padding-left: -150px;
}