.cta-main-area-wrapper-one {
    background-image: url(../images/cta/01.webp);
    height: 175px;
    background-color: #20282D;
    border-radius: 10px;
    display: flex;
    align-items: center;
    padding: 80px;
    justify-content: space-between;

    @media #{$md-layout} {
        height: auto;
        flex-direction: column;
    }

    @media #{$sm-layout} {
        flex-direction: column;
        height: auto;
        padding: 25px;
    }

    @media #{$large-mobile} {
        flex-direction: column;
        height: auto;
        padding: 25px;
        text-align: center;
    }


    .left-areas {
        span {
            text-transform: uppercase;
            letter-spacing: 1.5px;
            font-size: 16px;
        }

        .title {
            font-size: 36px;
            margin-top: 10px;
            margin-bottom: 0;

            @media #{$sm-layout} {
                font-size: 26px;
                margin-bottom: 15px;
            }

            @media #{$large-mobile} {
                font-size: 26px;
                margin-bottom: 15px;
            }
        }

        * {
            color: #fff;
        }
    }

    .right-area {
        @media #{$large-mobile} {
            width: 100%;
        }

        .inpur-area-main {
            position: relative;

            input {
                height: 63px;
                background: #FFFFFF;
                color: #8B8F99;
                width: 547px;
                padding-right: 200px;

                @media #{$sm-layout} {
                    width: 100%;
                }

                @media #{$large-mobile} {
                    width: 100%;
                    padding-right: 0;
                }
            }

            button {
                position: absolute;
                right: 10px;
                top: 50%;
                height: 45px;
                transform: translateY(-50%);
                border-radius: 6px;

                @media #{$large-mobile} {
                    position: unset;
                    transform: none;
                    margin: auto;
                    margin-top: 15px;
                }
            }
        }
    }
}


.container-full {
    width: 100%;
}

.rts-call-to-action-area-two {
    background-image: url(../images/cta/02.webp);
    height: 280px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    @media #{$sm-layout} {
        height: auto;
        flex-direction: column;
        align-items: flex-start;
    }
}

.cta-style-two-area {
    display: flex;
    align-items: center;
    justify-content: space-between;

    @media #{$sm-layout} {
        flex-direction: column;
        align-items: flex-start;
        padding: 25px 0;
    }

    .title {
        font-size: 40px;
        line-height: 1.4;
        @media #{$md-layout} {
            font-size: 26px;
        }
        @media #{$sm-layout} {
            font-size: 26px;

            br {
                display: none;
            }
        }
    }

    * {
        color: #fff;
    }
}

.rts-call-to-action-area-three {
    height: 361px;
    background-image: url(../images/cta/03.webp);
    display: flex;
    align-items: center;

    .cta-main-wrapper-inner {
        display: flex;
        align-items: center;
        justify-content: space-between;
        @media #{$smlg-device} {
            flex-wrap: wrap;
        }
        @media #{$mdsm-layout} {
            flex-direction: column;
            align-items: flex-start;
        }

        .title {
            font-size: 44px;
            font-weight: 400;
            color: #fff;
            line-height: 1.4;

            @media #{$smlg-device} {
                font-size: 30px;

                br {
                    display: none;
                }
            }

            span {
                font-weight: 700;
            }
        }

        .call-area {
            display: flex;
            align-items: center;
            gap: 17px;

            .icon {
                height: 82px;
                width: 82px;
                background: #fff;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;

                i {
                    font-size: 22px;
                    color: #20282D;
                }
            }

            a {
                font-size: 60px;
                color: #fff;

                @media #{$laptop-device} {
                    font-size: 38px;
                }

                @media #{$smlg-device} {
                    font-size: 36px;
                }

                @media #{$large-mobile} {
                    font-size: 32px;
                }
            }
        }
    }
}

.bg_call-toaction-4 {
    background-image: url(../images/cta/04.webp);

    .cta-four-wrapper {
        display: flex;
        align-items: center;
        gap: 31px;
        justify-content: space-between;

        @media #{$mdsm-layout} {
            flex-direction: column;
            align-items: flex-start;
            gap: 30px;
        }

        .left {
            display: flex;
            align-items: center;
            gap: 40px;

            @media #{$mdsm-layout} {
                flex-direction: column;
                align-items: flex-start;
                gap: 30px;
            }

            .title {
                font-size: 36px;
                font-weight: 800;
                margin: 0;
                line-height: 1.5;
                text-transform: uppercase;
                -webkit-text-stroke-color: #1C2539;
                -webkit-text-stroke: 1px;

                @media #{$mdsm-layout} {
                    font-size: 22px;
                }


                span {
                    -webkit-text-stroke-color: #1C2539;
                    -webkit-text-stroke: 1px;
                    -webkit-text-fill-color: transparent;
                    letter-spacing: 3px;
                    font-size: 30px;
                }
            }
        }
    }
}

.rts-cta-area-six {
    background-image: url(../images/cta/06.webp);
}

.cta-main-wrapper-six {
    display: flex;
    align-items: center;
    justify-content: space-between;

    @media #{$sm-layout} {
        flex-wrap: wrap;
        gap: 30px;
    }

    .title {
        font-size: 36px;
        margin-bottom: 0;
        line-height: 1.3;

        @media #{$large-mobile} {
            font-size: 24px;
        }
    }

    * {
        color: #fff;
    }
}

.rts-subscribe-area-8 {
    background-image: url(../images/cta/08.webp);

    form {
        position: relative;
        max-width: 547px;
        margin: auto;
        margin-top: 35px;

        input {
            height: 63px;
            background: var(--color-primary);
            color: #fff;
            border-radius: 6px;
        }

        button {
            position: absolute !important;
            right: 10px;
            border-radius: 6px;
            left: auto;
            top: 50%;
            transform: translateY(-50%);
            height: 43px;
        }
    }
}

.bg_cts-10 {
    background-image: url(../images/cta/09.webp);
}

.cta-style-10-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;

    @media #{$mdsm-layout} {
        flex-direction: column;
        align-items: flex-start;
    }

    .left-side {
        span {
            display: block;
            margin-bottom: 15px;
        }

        .title {
            font-size: 60px;
            color: #1C2539;

            @media #{$smlg-device} {
                font-size: 32px;
            }

            @media #{$sm-layout} {
                font-size: 32px;
                line-height: 1.3;
            }
        }
    }

    .right-side {
        form {
            position: relative;
            width: 547px;

            @media #{$sm-layout} {
                width: 100%;
            }

            input {
                background: var(--color-primary);
                height: 63px;
                border-radius: 6px;
                color: #8B8F99;
                padding-right: 200px;
            }

            button {
                position: absolute;
                right: 10px;
                top: 50%;
                transform: translateY(-50%);
                height: 43px;
                border-radius: 6px;
            }
        }
    }
}

.rts-cta-area-inner {
    background-image: url(../images/cta/10.webp);
}

.cta-inner-content-inner-page {
    display: flex;
    align-items: center;
    justify-content: space-between;

    @media #{$smlg-device} {
        flex-direction: column;
        align-items: flex-start;
        gap: 25px;

        br {
            display: none;
        }
    }

    .left-side-content {
        .title {
            font-size: 60px;

            @media #{$mdsm-layout} {
                font-size: 44px;
                line-height: 1.5;
            }

            @media #{$large-mobile} {
                font-size: 28px;
            }
        }
    }

    form {
        position: relative;

        @media #{$sm-layout} {
            width: 100%;
        }

        input {
            height: 63px;
            width: 547px;
            background: var(--color-primary);
            color: #8B8F99;

            @media #{$sm-layout} {
                width: 100%;
            }
        }

        button.rts-btn {
            position: absolute;
            right: 10px;
            height: 43px;
            top: 50%;
            transform: translateY(-50%);
            border-radius: 6px;
        }
    }
}