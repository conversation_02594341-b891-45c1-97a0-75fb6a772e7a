.business-boost-wrapper-main {
    background: #F2F2F2;
    border-bottom: 5px solid #20282D;
    box-shadow: 0px 22px 23px rgba(0, 0, 0, 0.07);

    .business-marklting-area-thumb {
        img {
            clip-path: polygon(90.979% 76.176%, 90.979% 76.176%, 87.388% 81.516%, 83.451% 86.042%, 79.278% 89.812%, 74.979% 92.886%, 70.667% 95.322%, 66.452% 97.179%, 62.445% 98.517%, 58.756% 99.393%, 55.497% 99.868%, 52.778% 99.999%, 52.778% 99.999%, 42.4% 98.943%, 33.16% 95.938%, 25.057% 91.227%, 18.089% 85.055%, 12.257% 77.665%, 7.557% 69.302%, 3.989% 60.209%, 1.553% 50.631%, 0.245% 40.812%, 0.065% 30.995%, 0.065% 30.995%, 1.106% 22.187%, 3.343% 15.123%, 6.605% 9.637%, 10.719% 5.561%, 15.511% 2.73%, 20.809% 0.978%, 26.44% 0.137%, 32.231% 0.041%, 38.009% 0.525%, 43.602% 1.421%, 43.602% 1.421%, 48.661% 2.87%, 53.065% 5.004%, 56.977% 7.646%, 60.562% 10.618%, 63.982% 13.743%, 67.403% 16.844%, 70.987% 19.742%, 74.9% 22.261%, 79.304% 24.222%, 84.363% 25.45%, 84.363% 25.45%, 89.337% 26.87%, 93.369% 29.444%, 96.454% 33.043%, 98.585% 37.539%, 99.755% 42.803%, 99.959% 48.709%, 99.19% 55.127%, 97.441% 61.93%, 94.706% 68.989%, 90.979% 76.176%);
            height: 480px;
            width: auto;
            object-fit: cover;

            @media #{$large-mobile} {
                clip-path: none;
                margin-bottom: 30px;
                height: auto;
            }
            @media(max-width:991px){
                margin-bottom: 40px;
            }
        }
    }
}

.boosting-business-right-area {
    .pre {
        color: #000000;
        text-transform: uppercase;
        padding: 0;
        border: none;
        font-size: 16px;
        letter-spacing: 1px;
    }

    .feature-one-wrapper {
        display: flex;
        flex-wrap: wrap;

        .single-feature-one {
            display: flex;
            align-items: center;
            padding: 15px 29px;
            background: transparent;
            border: 1px solid #d8d8d8;
            border-radius: 15px;
            max-width: 290px;
            margin-right: 25px;
            margin-bottom: 25px;
            cursor: pointer;
            transition: 0.3s;
            width: 282px;

            i {
                padding: 5px;
                background: var(--color-primary);
                border-radius: 50%;
                color: #fff;
                margin-right: 15px;
                font-size: 14px;
                line-height: 12px;
            }

            p {
                color: #1C2539;
                font-weight: 500;
                font-size: 18px;
            }

            &:hover {
                border: 1px solid #EEEEEE;
                box-shadow: 0px 9px 18px rgba(24, 16, 16, 0.05);
                border-radius: 15px;
                background: #fff;
            }

            // &.active {
            //     border: 1px solid #EEEEEE;
            //     box-shadow: 0px 9px 18px rgba(24, 16, 16, 0.05);
            //     border-radius: 15px;
            //     background: #fff;
            // }
        }
    }
}