.header-transparent {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    z-index: 100;

    &.header--sticky.sticky {
        position: fixed !important;
        top: 0;
        display: block;
        backdrop-filter: blur(9px);
        z-index: 999;
        width: 100%;
        box-shadow: 0px 7px 18px rgba(24, 16, 16, 0.0509803922);
        background: #20282d;
    }
}

.header-transparent-main-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .main-nav>a {
        color: #fff !important;
    }

    .has-dropdown::after,
    .has-dropdown.mega-menu>a::after {
        color: #fff !important;
    }

    .rts-btn.btn-primary {
        border-radius: 100px;
        @media #{$large-mobile} {
            display: none;
        }
    }
}

.in-full-screen .header-transparent-main-wrapper{

    position: relative;

    .rts-mega-menu {
        left: 50%;
        width: 80%;
        transform: translateX(-50%) scaleY(0);
    }

    .has-dropdown.mega-menu:hover .rts-mega-menu {
        transform: translateX(-50%) scaleY(1);
    }
}