# النشر والإطلاق - الأسبوع السادس

## اليوم 36-38: إعداد بيئة الإنتاج

### 1. إعداد الخادم (Server Setup)
```bash
# Server Configuration Script
#!/bin/bash

# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y nginx mysql-server php8.2-fpm php8.2-mysql php8.2-xml php8.2-mbstring php8.2-curl php8.2-zip php8.2-gd

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# Install Node.js and npm
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Configure Nginx
sudo tee /etc/nginx/sites-available/paper-company << EOF
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    root /var/www/paper-company/public;
    index index.php index.html;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;

    # Cache static files
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|txt|tar|woff|svg|ttf|eot|woff2)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location / {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME \$realpath_root\$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }
}
EOF

# Enable site
sudo ln -s /etc/nginx/sites-available/paper-company /etc/nginx/sites-enabled/
sudo nginx -t && sudo systemctl reload nginx

# Configure SSL with Let's Encrypt
sudo apt install -y certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com -d www.your-domain.com
```

### 2. إعداد قاعدة البيانات للإنتاج
```sql
-- Production Database Setup
CREATE DATABASE paper_company_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

CREATE USER 'paper_user'@'localhost' IDENTIFIED BY 'secure_password_here';
GRANT SELECT, INSERT, UPDATE, DELETE ON paper_company_prod.* TO 'paper_user'@'localhost';

-- Optimize MySQL for production
SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB
SET GLOBAL query_cache_size = *********; -- 256MB
SET GLOBAL max_connections = 200;

-- Create indexes for better performance
USE paper_company_prod;

-- Products table indexes
ALTER TABLE products ADD INDEX idx_category_status (category, status);
ALTER TABLE products ADD INDEX idx_created_at (created_at);
ALTER TABLE products ADD FULLTEXT idx_search (name, description);

-- Analytics table indexes
ALTER TABLE analytics_events ADD INDEX idx_event_type_date (event_type, created_at);
ALTER TABLE analytics_events ADD INDEX idx_session_id (session_id);

-- Inquiries table indexes
ALTER TABLE inquiries ADD INDEX idx_status_date (status, created_at);
```

### 3. إعداد متغيرات البيئة للإنتاج
```bash
# .env.production
APP_NAME="شركة الورق المتقدمة"
APP_ENV=production
APP_KEY=base64:your-generated-app-key-here
APP_DEBUG=false
APP_URL=https://your-domain.com

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=paper_company_prod
DB_USERNAME=paper_user
DB_PASSWORD=secure_password_here

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# Analytics
GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
FACEBOOK_PIXEL_ID=your-pixel-id

# Push Notifications
VAPID_PUBLIC_KEY=your-vapid-public-key
VAPID_PRIVATE_KEY=your-vapid-private-key

# CDN Configuration
CDN_URL=https://cdn.your-domain.com
```

## اليوم 39-40: نشر التطبيق

### 1. سكريبت النشر التلقائي
```bash
#!/bin/bash
# deploy.sh - Automated Deployment Script

set -e

echo "🚀 بدء عملية النشر..."

# Variables
PROJECT_DIR="/var/www/paper-company"
BACKUP_DIR="/var/backups/paper-company"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Create backup
echo "📦 إنشاء نسخة احتياطية..."
mkdir -p $BACKUP_DIR
tar -czf $BACKUP_DIR/backup_$TIMESTAMP.tar.gz -C $PROJECT_DIR .

# Pull latest changes
echo "📥 تحديث الكود..."
cd $PROJECT_DIR
git pull origin main

# Install/Update dependencies
echo "📚 تحديث المكتبات..."
composer install --no-dev --optimize-autoloader
npm ci --production

# Build assets
echo "🔨 بناء الأصول..."
npm run production

# Database migrations
echo "🗄️ تحديث قاعدة البيانات..."
php artisan migrate --force

# Clear and cache
echo "🧹 تنظيف وتحسين الذاكرة المؤقتة..."
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear

php artisan config:cache
php artisan route:cache
php artisan view:cache

# Optimize autoloader
php artisan optimize

# Set permissions
echo "🔐 تعيين الصلاحيات..."
sudo chown -R www-data:www-data $PROJECT_DIR
sudo chmod -R 755 $PROJECT_DIR
sudo chmod -R 775 $PROJECT_DIR/storage
sudo chmod -R 775 $PROJECT_DIR/bootstrap/cache

# Restart services
echo "🔄 إعادة تشغيل الخدمات..."
sudo systemctl reload nginx
sudo systemctl restart php8.2-fpm

# Health check
echo "🏥 فحص صحة التطبيق..."
response=$(curl -s -o /dev/null -w "%{http_code}" https://your-domain.com)
if [ $response -eq 200 ]; then
    echo "✅ النشر تم بنجاح!"
    
    # Send notification
    curl -X POST "https://api.telegram.org/bot$TELEGRAM_BOT_TOKEN/sendMessage" \
         -d chat_id="$TELEGRAM_CHAT_ID" \
         -d text="✅ تم نشر موقع شركة الورق بنجاح - $TIMESTAMP"
else
    echo "❌ فشل النشر - رمز الاستجابة: $response"
    
    # Rollback
    echo "🔄 استرجاع النسخة السابقة..."
    tar -xzf $BACKUP_DIR/backup_$TIMESTAMP.tar.gz -C $PROJECT_DIR
    
    exit 1
fi

echo "🎉 تم الانتهاء من النشر!"
```

### 2. إعداد CI/CD مع GitHub Actions
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: testing
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, xml, ctype, iconv, intl, pdo_mysql, dom, filter, gd, iconv, json, mbstring, pdo
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Copy .env
      run: php -r "file_exists('.env') || copy('.env.example', '.env');"
    
    - name: Install Dependencies
      run: |
        composer install -q --no-ansi --no-interaction --no-scripts --no-progress --prefer-dist
        npm ci
    
    - name: Generate key
      run: php artisan key:generate
    
    - name: Directory Permissions
      run: chmod -R 777 storage bootstrap/cache
    
    - name: Run Database Migrations
      env:
        DB_CONNECTION: mysql
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: testing
        DB_USERNAME: root
        DB_PASSWORD: password
      run: php artisan migrate
    
    - name: Build Assets
      run: npm run production
    
    - name: Execute tests (Unit and Feature tests) via PHPUnit
      env:
        DB_CONNECTION: mysql
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: testing
        DB_USERNAME: root
        DB_PASSWORD: password
      run: vendor/bin/phpunit

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.KEY }}
        script: |
          cd /var/www/paper-company
          ./deploy.sh
```

## اليوم 41-42: مراقبة الأداء والأمان

### 1. إعداد مراقبة الأداء
```php
// app/Http/Middleware/PerformanceMonitoring.php
class PerformanceMonitoring
{
    public function handle($request, Closure $next)
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        
        $response = $next($request);
        
        $endTime = microtime(true);
        $endMemory = memory_get_usage();
        
        $executionTime = ($endTime - $startTime) * 1000; // milliseconds
        $memoryUsage = $endMemory - $startMemory;
        
        // Log slow requests
        if ($executionTime > 1000) { // > 1 second
            Log::warning('Slow request detected', [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'execution_time' => $executionTime,
                'memory_usage' => $memoryUsage,
                'user_agent' => $request->userAgent(),
                'ip' => $request->ip()
            ]);
        }
        
        // Add performance headers
        $response->headers->set('X-Response-Time', round($executionTime, 2) . 'ms');
        $response->headers->set('X-Memory-Usage', round($memoryUsage / 1024 / 1024, 2) . 'MB');
        
        return $response;
    }
}
```

### 2. إعداد الأمان
```php
// app/Http/Middleware/SecurityHeaders.php
class SecurityHeaders
{
    public function handle($request, Closure $next)
    {
        $response = $next($request);
        
        // Security headers
        $response->headers->set('X-Frame-Options', 'SAMEORIGIN');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        $response->headers->set('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
        
        // Content Security Policy
        $csp = "default-src 'self'; " .
               "script-src 'self' 'unsafe-inline' https://www.google-analytics.com https://www.googletagmanager.com; " .
               "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " .
               "font-src 'self' https://fonts.gstatic.com; " .
               "img-src 'self' data: https:; " .
               "connect-src 'self' https://www.google-analytics.com;";
        
        $response->headers->set('Content-Security-Policy', $csp);
        
        return $response;
    }
}
```

### 3. إعداد النسخ الاحتياطية التلقائية
```bash
#!/bin/bash
# backup.sh - Automated Backup Script

BACKUP_DIR="/var/backups/paper-company"
PROJECT_DIR="/var/www/paper-company"
DB_NAME="paper_company_prod"
DB_USER="paper_user"
DB_PASS="secure_password_here"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR/daily
mkdir -p $BACKUP_DIR/weekly
mkdir -p $BACKUP_DIR/monthly

# Database backup
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME | gzip > $BACKUP_DIR/daily/db_$TIMESTAMP.sql.gz

# Files backup
tar -czf $BACKUP_DIR/daily/files_$TIMESTAMP.tar.gz -C $PROJECT_DIR storage public/uploads

# Weekly backup (every Sunday)
if [ $(date +%u) -eq 7 ]; then
    cp $BACKUP_DIR/daily/db_$TIMESTAMP.sql.gz $BACKUP_DIR/weekly/
    cp $BACKUP_DIR/daily/files_$TIMESTAMP.tar.gz $BACKUP_DIR/weekly/
fi

# Monthly backup (first day of month)
if [ $(date +%d) -eq 01 ]; then
    cp $BACKUP_DIR/daily/db_$TIMESTAMP.sql.gz $BACKUP_DIR/monthly/
    cp $BACKUP_DIR/daily/files_$TIMESTAMP.tar.gz $BACKUP_DIR/monthly/
fi

# Clean old backups (keep 7 days, 4 weeks, 12 months)
find $BACKUP_DIR/daily -name "*.gz" -mtime +7 -delete
find $BACKUP_DIR/weekly -name "*.gz" -mtime +28 -delete
find $BACKUP_DIR/monthly -name "*.gz" -mtime +365 -delete

echo "Backup completed: $TIMESTAMP"
```

### 4. إعداد Cron Jobs
```bash
# Add to crontab: crontab -e

# Backup every day at 2 AM
0 2 * * * /var/www/paper-company/backup.sh

# Clear logs weekly
0 3 * * 0 /usr/bin/find /var/www/paper-company/storage/logs -name "*.log" -mtime +7 -delete

# Update sitemap daily
0 4 * * * cd /var/www/paper-company && php artisan sitemap:generate

# Send analytics reports weekly
0 9 * * 1 cd /var/www/paper-company && php artisan analytics:weekly-report

# Clean temporary files
0 5 * * * /usr/bin/find /tmp -name "*.tmp" -mtime +1 -delete
```

## الملفات المطلوبة للأسبوع السادس:

```
scripts/
├── deploy.sh
├── backup.sh
├── server-setup.sh
└── health-check.sh

.github/workflows/
└── deploy.yml

app/Http/Middleware/
├── PerformanceMonitoring.php
├── SecurityHeaders.php
└── RateLimiting.php

config/
├── production.php
└── monitoring.php
```
